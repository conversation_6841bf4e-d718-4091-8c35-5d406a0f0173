import axios from 'axios';
import {Lead} from '../../domain/crm/Lead';
import { Socio } from '../../domain/crm/CrmEmpresa';
import { Resposta } from '../../utils/Resposta';

interface BitrixConfig {
  baseUrl: string;
  userId: string;
  webhook: string;
}

interface BitrixPhone {
  VALUE: string;
  VALUE_TYPE: 'WORK' | 'MOBILE' | 'HOME' | 'OTHER';
}

interface BitrixEmail {
  VALUE: string;
  VALUE_TYPE: 'WORK' | 'HOME' | 'OTHER';
}

interface BitrixWeb {
  VALUE: string;
  VALUE_TYPE: 'WORK' | 'HOME' | 'OTHER';
}

interface BitrixContactFields {
  NAME?: string;
  LAST_NAME?: string;
  PHONE?: BitrixPhone[];
  EMAIL?: BitrixEmail[];
  COMMENTS?: string;
  ASSIGNED_BY_ID?: number;
  // Campos customizados podem ser adicionados com prefixo UF_
  [key: string]: any;
}

interface BitrixContactPayload {
  fields: BitrixContactFields;
  params?: {
    REGISTER_SONET_EVENT?: 'Y' | 'N'; // Notificar responsável
  };
}

interface BitrixLeadFields {
  TITLE?: string;
  NAME?: string;
  LAST_NAME?: string;
  COMPANY_TITLE?: string;
  STATUS_ID?: string;
  SOURCE_ID?: string;
  SOURCE_DESCRIPTION?: string;
  PHONE?: BitrixPhone[];
  EMAIL?: BitrixEmail[];
  WEB?: BitrixWeb[];
  COMMENTS?: string;
  ASSIGNED_BY_ID?: number;
  CONTACT_ID?: number; // Vinculação com contato
  // Campos customizados podem ser adicionados com prefixo UF_
  [key: string]: any;
}

interface BitrixLeadPayload {
  fields: BitrixLeadFields;
  params?: {
    REGISTER_SONET_EVENT?: 'Y' | 'N'; // Notificar responsável
  };
}

interface CustomFieldMapping {
  label: string;
  fieldName: string;
  leadProperty: string;
  transform?: (value: any) => any;
}

export class BitrixService {
  private config: BitrixConfig;
  private customFieldsCache: Map<string, any> = new Map();
  private customFieldsMappingCache: Map<string, string> = new Map();


  /**
   * Configuração de mapeamento dos custom fields baseado no JSON real do Bitrix
   */
  private readonly CUSTOM_FIELD_MAPPINGS: CustomFieldMapping[] = [
    {
      label: "Instagram",
      fieldName: "UF_CRM_1615222177542",
      leadProperty: "getLinkInstagram", // Método que busca link do Instagram
      transform: (value) => value || ''
    },
    {
      label: "Telefone de contato ",
      fieldName: "UF_CRM_1621521259444",
      leadProperty: "telefone",
      transform: (value) => {
        if (!value) return '';
        // Return only digits for the custom phone field (Bitrix expects numbers only)
        const numeroLimpo = value.replace(/\D/g, '');
        return numeroLimpo;
      }
    },
    {
      label: "Site",
      fieldName: "UF_CRM_1621947262974",
      leadProperty: "getLinkSite", // Método que busca link do site
      transform: (value) => value || ''
    },
    {
      label: "CNPJ",
      fieldName: "UF_CRM_1621948447906",
      leadProperty: "cnpj"
    },
    {
      label: "Rapport",
      fieldName: "UF_CRM_1622222037",
      leadProperty: "observacoes"
    },
    {
      label: "Link Ifood",
      fieldName: "UF_CRM_1623334200",
      leadProperty: "getLinkIfood", // Método que busca link do iFood
      transform: (value) => value || ''
    },
    {
      label: "Link Uber Eats",
      fieldName: "UF_CRM_1623334228",
      leadProperty: "linkUberEats"
    },
    {
      label: "Link Rappi",
      fieldName: "UF_CRM_1623431063",
      leadProperty: "linkRappi"
    },
    {
      label: "Link 99Food",
      fieldName: "UF_CRM_1634307256707",
      leadProperty: "link99Food"
    },
    {
      label: "Link do ifood ",
      fieldName: "UF_CRM_1650463024814",
      leadProperty: "getLinkIfood", // Duplicado - usar o mesmo método
      transform: (value) => value || ''
    },
    {
      label: "Site do Concorrente",
      fieldName: "UF_CRM_1623263814",
      leadProperty: "getLinkConcorrente", // Método que busca link de concorrente
      transform: (value) => value || ''
    },
    {
      label: "Concorrente",
      fieldName: "UF_CRM_1623263569",
      leadProperty: "getConcorrente", // Método que obtém o concorrente do lead
      transform: (value) => value || 'Não descobri'
    },
    {
      label: "Todos os Links",
      fieldName: "UF_CRM_1749901110452",
      leadProperty: "getAllLinksUrls", // Método que retorna array de URLs
      transform: (value) => Array.isArray(value) ? value : []
    }
  ];


  constructor(config: BitrixConfig) {
    this.config = config;
  }

  /**
   * Valida estrutura do payload antes de enviar para o Bitrix
   */
  private validarPayload(payload: any): void {
    const issues: string[] = [];

    console.log('VALIDAÇÃO: Verificando estrutura do payload...');

    if (!payload.fields) {
      issues.push('Campo "fields" obrigatório está ausente');
    } else {
      const fields = payload.fields;

      // Verificar campos obrigatórios
      if (!fields.TITLE) {
        issues.push('Campo TITLE obrigatório está ausente');
      }

      if (!fields.NAME && !fields.LAST_NAME) {
        issues.push('Pelo menos um dos campos NAME ou LAST_NAME deve estar presente');
      }

      // Verificar formato dos arrays de contato
      if (fields.PHONE && !Array.isArray(fields.PHONE)) {
        issues.push('Campo PHONE deve ser um array');
      }

      if (fields.EMAIL && !Array.isArray(fields.EMAIL)) {
        issues.push('Campo EMAIL deve ser um array');
      }

      if (fields.WEB && !Array.isArray(fields.WEB)) {
        issues.push('Campo WEB deve ser um array');
      }

      // Verificar estrutura dos telefones
      if (fields.PHONE && Array.isArray(fields.PHONE)) {
        fields.PHONE.forEach((phone: any, index: number) => {
          if (!phone.VALUE) {
            issues.push(`PHONE[${index}]: Campo VALUE é obrigatório`);
          }
          if (!phone.VALUE_TYPE || !['WORK', 'MOBILE', 'HOME', 'OTHER'].includes(phone.VALUE_TYPE)) {
            issues.push(`PHONE[${index}]: Campo VALUE_TYPE deve ser WORK, MOBILE, HOME ou OTHER`);
          }
        });
      }

      // Verificar estrutura dos emails
      if (fields.EMAIL && Array.isArray(fields.EMAIL)) {
        fields.EMAIL.forEach((email: any, index: number) => {
          if (!email.VALUE) {
            issues.push(`EMAIL[${index}]: Campo VALUE é obrigatório`);
          }
          if (!email.VALUE_TYPE || !['WORK', 'HOME', 'OTHER'].includes(email.VALUE_TYPE)) {
            issues.push(`EMAIL[${index}]: Campo VALUE_TYPE deve ser WORK, HOME ou OTHER`);
          }
        });
      }

      // Verificar campos customizados
      const customFields = Object.keys(fields).filter(key => key.startsWith('UF_'));
      console.log(`VALIDAÇÃO: Encontrados ${customFields.length} campos customizados:`, customFields);

      // Verificar tamanhos dos campos
      if (fields.TITLE && fields.TITLE.length > 255) {
        issues.push('Campo TITLE excede 255 caracteres');
      }

      if (fields.COMMENTS && fields.COMMENTS.length > 1000) {
        issues.push('Campo COMMENTS excede 1000 caracteres');
      }
    }

    if (issues.length > 0) {
      console.error('VALIDAÇÃO: Problemas encontrados no payload:');
      issues.forEach((issue, index) => {
        console.error(`  ${index + 1}. ${issue}`);
      });
      console.warn('VALIDAÇÃO: Payload pode causar erro no Bitrix');
    } else {
      console.log('VALIDAÇÃO: ✓ Payload está válido');
    }
  }

  /**
   * Cria um contato no Bitrix24
   */
  async criarContato(lead: Lead): Promise<Resposta<number>> {
    try {
      const payload = this.converterLeadParaContato(lead);
      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.contact.add.json`;

      console.log('BITRIX: Criando contato:', JSON.stringify(payload, null, 2));
      console.log('BITRIX: URL:', url);

      const response = await axios.post(url, payload);

      if (response.data.error) {
        console.error('BITRIX: Erro ao criar contato:', response.data);
        return Resposta.erro(`Erro do Bitrix ao criar contato: ${response.data.error_description}`) as any;
      }

      const contactId = parseInt(response.data.result, 10);
      console.log('BITRIX: Contato criado com sucesso. ID:', contactId);

      return Resposta.sucesso(contactId) as Resposta<number>;
    } catch (error) {
      console.error('BITRIX: Erro na requisição de contato:', error);
      return Resposta.erro(`Erro ao conectar com Bitrix para criar contato: ${error.message}`) as any;
    }
  }


  /**
   * Cria múltiplos contatos no Bitrix24 para todos os sócios de um lead
   */
  async criarMultiplosContatos(lead: Lead): Promise<Resposta<{ contatosPrincipais: number[], contatosSecundarios:
      number[], erros: string[] }>> {
    const timestamp = new Date().toISOString();
    console.log(`\n====== BITRIX MULTIPLE CONTACTS CREATION START [${timestamp}] ======`);


    const socios = lead.getSocios();
    const contatosPrincipais: number[] = [];
    const contatosSecundarios: number[] = [];
    const erros: string[] = [];

    console.log('BITRIX: Criando contatos para', socios.length, 'sócios...');

    if (socios.length === 0) {
      console.log('BITRIX: Nenhum sócio encontrado, usando contato principal do lead');
      const resultadoContato = await this.criarContato(lead);

      if (resultadoContato.sucesso) {
        contatosPrincipais.push(resultadoContato.data);
      } else {
        erros.push(`Erro ao criar contato principal: ${resultadoContato.erro}`);
      }
    } else {
      // Criar contatos para todos os sócios
      for (let i = 0; i < socios.length; i++) {
        const socio = socios[i];
        console.log(`BITRIX: Criando contato ${i + 1}/${socios.length} para sócio: ${socio.nome} (Principal: ${socio.principal})`);

        try {
          const resultadoContato = await this.criarContatoParaSocio(lead, socio);

          if (resultadoContato.sucesso) {
            if (socio.principal) {
              contatosPrincipais.push(resultadoContato.data);
              console.log(`BITRIX: ✓ Contato principal criado para ${socio.nome}, ID:`, resultadoContato.data);
            } else {
              contatosSecundarios.push(resultadoContato.data);
              console.log(`BITRIX: ✓ Contato secundário criado para ${socio.nome}, ID:`, resultadoContato.data);
            }
          } else {
            const mensagemErro = `Erro ao criar contato para ${socio.nome}: ${resultadoContato.erro}`;
            erros.push(mensagemErro);
            console.error('BITRIX: ✗', mensagemErro);
          }

          // Pequena pausa entre criações para evitar rate limiting
          if (i < socios.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 500));
          }

        } catch (error) {
          const mensagemErro = `Erro inesperado ao criar contato para ${socio.nome}: ${error.message}`;
          erros.push(mensagemErro);
          console.error('BITRIX: ✗', mensagemErro);
        }
      }
    }

    const resultado = {
      contatosPrincipais,
      contatosSecundarios,
      erros
    };

    console.log(`\nBITRIX: === RESUMO DA CRIAÇÃO DE CONTATOS ===`);
    console.log(`✓ Contatos principais criados: ${contatosPrincipais.length}`);
    console.log(`✓ Contatos secundários criados: ${contatosSecundarios.length}`);
    console.log(`✗ Erros: ${erros.length}`);

    if (erros.length > 0) {
      console.log('Erros detalhados:', erros);
    }

    console.log(`====== BITRIX MULTIPLE CONTACTS CREATION END [${new Date().toISOString()}] ======\n`);

    return Resposta.sucesso(resultado) as Resposta<{ contatosPrincipais: number[], contatosSecundarios: number[], erros: string[] }>;
  }

  /**
   * Cria um contato específico para um sócio
   */
  private async criarContatoParaSocio(lead: Lead, socio: Socio): Promise<Resposta<number>> {
    try {
      const payload = this.converterSocioParaContato(lead, socio);
      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.contact.add.json`;

      console.log(`BITRIX: Criando contato para sócio ${socio.nome}:`, JSON.stringify(payload, null, 2));

      const response = await axios.post(url, payload);

      if (response.data.error) {
        console.error(`BITRIX: Erro ao criar contato para ${socio.nome}:`, response.data);
        return Resposta.erro(`Erro do Bitrix ao criar contato para ${socio.nome}: ${response.data.error_description}`) as any;
      }

      const contactId = parseInt(response.data.result);
      console.log(`BITRIX: Contato criado para ${socio.nome} com sucesso. ID:`, contactId);

      return Resposta.sucesso(contactId) as Resposta<number>;
    } catch (error) {
      console.error(`BITRIX: Erro na requisição de contato para ${socio.nome}:`, error);
      return Resposta.erro(`Erro ao conectar com Bitrix para criar contato para ${socio.nome}: ${error.message}`) as any;
    }
  }

  /**
   * Cria um lead no Bitrix24 (agora com criação de contato primeiro)
   */
  async criarLead(lead: Lead): Promise<Resposta<number>> {
    const timestamp = new Date().toISOString();
    console.log(`\n====== BITRIX LEAD CREATION START [${timestamp}] ======`);

    // DEBUG: Verificação inicial do parâmetro
    console.log('BITRIX criarLead DEBUG: Tipo do parâmetro:', typeof lead);
    console.log('BITRIX criarLead DEBUG: Constructor name:', lead?.constructor?.name);
    console.log('BITRIX criarLead DEBUG: É objeto?', lead && typeof lead === 'object');
    console.log('BITRIX criarLead DEBUG: Tem propriedade hasSocios?', 'hasSocios' in lead);
    console.log('BITRIX criarLead DEBUG: Tipo de hasSocios:', typeof lead?.hasSocios);


    console.log('BITRIX: Lead object received:', {
      id: lead.id,
      empresa: lead.empresa,
      nomeResponsavel: lead.nomeResponsavel,
      telefone: lead.telefone,
      email: lead.crmEmpresa?.email,
      instagramHandle: lead.instagramHandle,
      etapa: lead.etapa,
      origem: lead.origem,
      segmento: lead.segmento,
      cnpj: lead.cnpj,
      linksCount: lead.links?.length || 0
    });

    try {
      let contactId: number | undefined;
      let contatosSecundarios: number[] = [];

      // Primeira etapa: Criar contatos para todos os sócios (se houver) ou contato principal
      console.log('BITRIX: === CRIANDO CONTATOS ===');

      // DEBUG: Verificar objeto lead
      console.log('BITRIX DEBUG: Tipo de lead:', typeof lead);
      console.log('BITRIX DEBUG: Constructor name:', lead?.constructor?.name);
      console.log('BITRIX DEBUG: Propriedades do lead:', Object.keys(lead));
      console.log('BITRIX DEBUG: Tem método hasSocios?', typeof lead.hasSocios === 'function');
      console.log('BITRIX DEBUG: Tem crmEmpresa?', !!lead.crmEmpresa);

      // Verificação segura
      let temSocios = false;
      try {
        if (typeof lead.hasSocios === 'function') {
          temSocios = lead.hasSocios();
          console.log('BITRIX DEBUG: hasSocios() retornou:', temSocios);
        } else {
          console.log('BITRIX DEBUG: lead.hasSocios não é uma função!');
          console.log('BITRIX DEBUG: Métodos disponíveis:', Object.getOwnPropertyNames(Object.getPrototypeOf(lead)));
        }
      } catch (erro) {
        console.error('BITRIX DEBUG: Erro ao chamar hasSocios():', erro);
      }

      if (temSocios) {
        console.log('BITRIX: Lead possui', lead.getQuantidadeSocios(), 'sócios. Criando múltiplos contatos...');

        const resultadoContatos = await this.criarMultiplosContatos(lead);

        if (resultadoContatos.sucesso) {
          const dados = resultadoContatos.data;

          // Usar o primeiro contato principal como contato do lead
          if (dados.contatosPrincipais.length > 0) {
            contactId = dados.contatosPrincipais[0];
            console.log('BITRIX: ✓ Contato principal selecionado para o lead, ID:', contactId);
          }

          // Armazenar contatos secundários para potencial uso futuro
          contatosSecundarios = [...dados.contatosPrincipais.slice(1), ...dados.contatosSecundarios];

          if (contatosSecundarios.length > 0) {
            console.log('BITRIX: ✓', contatosSecundarios.length, 'contatos secundários criados:', contatosSecundarios);
          }

          if (dados.erros.length > 0) {
            console.warn('BITRIX: ⚠ Alguns contatos falharam, mas prosseguindo com criação do lead');
            dados.erros.forEach(erro => console.warn('BITRIX: ✗', erro));
          }
        } else {
          console.warn('BITRIX: ✗ Falha ao criar múltiplos contatos:', resultadoContatos.erro);
          console.warn('BITRIX: Tentando fallback para contato principal...');

          // Fallback: tentar criar contato principal tradicional
          if (lead.nomeResponsavel && lead.nomeResponsavel.trim()) {
            const resultadoContato = await this.criarContato(lead);

            if (resultadoContato.sucesso) {
              contactId = resultadoContato.data;
              console.log('BITRIX: ✓ Contato fallback criado com sucesso, ID:', contactId);
            }
          }
        }
      } else if (lead.nomeResponsavel && lead.nomeResponsavel.trim()) {
        console.log('BITRIX: Nenhum sócio encontrado. Criando contato para responsável:', lead.nomeResponsavel);

        const resultadoContato = await this.criarContato(lead);

        if (resultadoContato.sucesso) {
          contactId = resultadoContato.data;
          console.log('BITRIX: ✓ Contato criado com sucesso, ID:', contactId);
        } else {
          console.warn('BITRIX: ✗ Falha ao criar contato:', resultadoContato.erro);
          console.warn('BITRIX: Prosseguindo sem vinculação de contato...');
        }
      } else {
        console.log('BITRIX: Nenhum responsável definido, criando lead sem contato vinculado');
      }

      console.log('\nBITRIX: === INICIANDO CRIAÇÃO DO LEAD ===');

      // Segunda etapa: Criar lead (com ou sem vinculação de contato)
      console.log('BITRIX: Convertendo lead para formato Bitrix...');
      const payload = this.converterLeadParaBitrix(lead, contactId);

      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.lead.add.json`;

      console.log('\nBITRIX: === PAYLOAD FINAL ===');
      console.log('URL:', url);
      console.log('Payload completo:', JSON.stringify(payload, null, 2));
      console.log('Payload size (bytes):', JSON.stringify(payload).length);

      // Validar payload antes de enviar
      console.log('\nBITRIX: === VALIDAÇÃO DO PAYLOAD ===');
      this.validarPayload(payload);

      console.log('\nBITRIX: === ENVIANDO REQUISIÇÃO ===');
      const startTime = Date.now();

      const response = await axios.post(url, payload, {
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'PromoKit-CRM/1.0'
        },
        timeout: 30000 // 30 segundos
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      console.log(`BITRIX: Resposta recebida em ${duration}ms`);
      console.log('BITRIX: Status HTTP:', response.status);
      console.log('BITRIX: Headers de resposta:', response.headers);
      console.log('BITRIX: Resposta completa:', JSON.stringify(response.data, null, 2));

      if (response.data.error) {
        console.error('\nBITRIX: === ERRO NA RESPOSTA ===');
        console.error('Código do erro:', response.data.error);
        console.error('Descrição do erro:', response.data.error_description);
        console.error('Dados completos do erro:', response.data);

        return Resposta.erro(`Erro do Bitrix [${response.data.error}]: ${response.data.error_description}`) as any;
      }

      if (!response.data.result) {
        console.error('BITRIX: Resposta sem campo "result":', response.data);
        return Resposta.erro('Resposta inválida do Bitrix: campo "result" não encontrado') as any;
      }

      const leadId = parseInt(response.data.result);

      if (isNaN(leadId)) {
        console.error('BITRIX: ID do lead inválido:', response.data.result);
        return Resposta.erro('ID do lead retornado pelo Bitrix é inválido') as any;
      }

      console.log(`\nBITRIX: === SUCESSO ===`);
      console.log('✓ Lead criado com sucesso!');
      console.log('✓ ID do lead:', leadId);

      if (contactId) {
        console.log('✓ Lead vinculado ao contato principal ID:', contactId);
      }

      if (contatosSecundarios.length > 0) {
        console.log('✓ Contatos secundários criados:', contatosSecundarios.length, 'contatos');
        console.log('✓ IDs dos contatos secundários:', contatosSecundarios);
      }

      console.log(`====== BITRIX LEAD CREATION END [${new Date().toISOString()}] ======\n`);

      return Resposta.sucesso(leadId) as Resposta<number>;

    } catch (error) {
      console.error('\nBITRIX: === ERRO NA REQUISIÇÃO ===');
      console.error('Tipo do erro:', error.constructor.name);
      console.error('Mensagem:', error.message);

      if (error.response) {
        console.error('Status HTTP:', error.response.status);
        console.error('Headers de resposta:', error.response.headers);
        console.error('Dados da resposta:', error.response.data);
      } else if (error.request) {
        console.error('Erro de rede - Request feito mas sem resposta:', error.request);
      } else {
        console.error('Erro na configuração:', error.message);
      }

      console.error('Stack trace:', error.stack);
      console.log(`====== BITRIX LEAD CREATION ERROR END [${new Date().toISOString()}] ======\n`);

      return Resposta.erro(`Erro ao conectar com Bitrix: ${error.message}`) as any;
    }
  }

  /**
   * Converte um sócio para contato do Bitrix
   */
  private converterSocioParaContato(lead: Lead, socio: Socio): BitrixContactPayload {
    // Extrair nome e sobrenome do sócio
    const nomeCompleto = socio.nome || '';
    const partesNome = nomeCompleto.trim().split(' ');
    const nome = partesNome[0] || '';
    const sobrenome = partesNome.slice(1).join(' ') || '';

    console.log(`BITRIX: Convertendo sócio ${socio.nome} para contato`);
    console.log('BITRIX: Nome:', nome);
    console.log('BITRIX: Sobrenome:', sobrenome);
    console.log('BITRIX: Cargo:', socio.cargo);
    console.log('BITRIX: Principal:', socio.principal);

    const fields: BitrixContactFields = {
      NAME: nome,
      LAST_NAME: sobrenome,
    };

    // Adicionar telefone preferencial se disponível
    const telefonePreferencial = this.obterTelefonePreferencial(lead);
    if (telefonePreferencial) {
      fields.PHONE = [{
        VALUE: this.formatarTelefone(telefonePreferencial),
        VALUE_TYPE: 'MOBILE'
      }];
      console.log('BITRIX: Telefone adicionado ao contato do sócio:', this.formatarTelefone(telefonePreferencial));
    }

    // Adicionar email da empresa se disponível
    if (lead.crmEmpresa?.email) {
      fields.EMAIL = [{
        VALUE: lead.crmEmpresa.email,
        VALUE_TYPE: 'WORK'
      }];
      console.log('BITRIX: Email adicionado ao contato do sócio:', lead.crmEmpresa.email);
    }

    // Comentários específicos do sócio
    const comentarios = this.montarComentariosSocio(lead, socio);
    fields.COMMENTS = comentarios;

    // Adicionar campo customizado para identificar o cargo/função
    if (socio.cargo) {
      fields.POST = socio.cargo; // Campo "Cargo" padrão do Bitrix
    }

    return {
      fields,
      params: {
        REGISTER_SONET_EVENT: 'Y' // Notificar responsável
      }
    };
  }

  /**
   * Monta comentários específicos para um sócio
   */
  private montarComentariosSocio(lead: Lead, socio: Socio): string {
    const comentarios: string[] = [];

    // Identificação do tipo de contato
    comentarios.push(socio.principal ? 'SÓCIO PRINCIPAL' : 'SÓCIO SECUNDÁRIO');
    comentarios.push(`Empresa: ${lead.empresa}`);
    comentarios.push(`Origem do lead: ${lead.origem}`);

    // Informações do cargo
    if (socio.cargo) {
      comentarios.push(`Cargo: ${socio.cargo}`);
    }

    // Data de entrada na sociedade
    if (socio.dataEntrada) {
      comentarios.push(`Sócio desde: ${socio.dataEntrada}`);
    }

    // Score de análise se disponível
    if (socio.scoreAnalise) {
      comentarios.push(`Score de análise: ${socio.scoreAnalise}%`);
    }

    // Motivo da seleção como principal
    if (socio.motivoSelecao) {
      comentarios.push(`Motivo da análise: ${socio.motivoSelecao}`);
    }

    // Dados da empresa
    if (lead.segmento) {
      comentarios.push(`Segmento: ${lead.segmento}`);
    }

    if (lead.cnpj) {
      comentarios.push(`CNPJ: ${lead.formatarCnpj()}`);
    }

    const comentarioCompleto = comentarios.join('\\n');

    // Limitar tamanho do comentário
    const LIMITE_CARACTERES = 900;
    if (comentarioCompleto.length > LIMITE_CARACTERES) {
      console.log(`BITRIX: Comentário do sócio muito longo (${comentarioCompleto.length} chars), truncando para ${LIMITE_CARACTERES}`);
      return comentarioCompleto.substring(0, LIMITE_CARACTERES) + '\\n\\n[...TEXTO TRUNCADO...]';
    }

    return comentarioCompleto;
  }

  /**
   * Converte um Lead do nosso sistema para contato do Bitrix
   */
  private converterLeadParaContato(lead: Lead): BitrixContactPayload {
    // Extrair nome e sobrenome
    const nomeResponsavel = lead.nomeResponsavel || '';
    const partesNome = nomeResponsavel.trim().split(' ');
    const nome = partesNome[0] || '';
    const sobrenome = partesNome.slice(1).join(' ') || '';

    console.log('BITRIX: Convertendo lead para contato');
    console.log('BITRIX: Nome completo:', nomeResponsavel);
    console.log('BITRIX: Nome:', nome);
    console.log('BITRIX: Sobrenome:', sobrenome);

    const fields: BitrixContactFields = {
      NAME: nome,
      LAST_NAME: sobrenome,
    };

    // Adicionar telefone preferencial se disponível
    const telefonePreferencial = this.obterTelefonePreferencial(lead);
    if (telefonePreferencial) {
      fields.PHONE = [{
        VALUE: this.formatarTelefone(telefonePreferencial),
        VALUE_TYPE: 'MOBILE'
      }];
      console.log('BITRIX: Telefone adicionado ao contato:', this.formatarTelefone(telefonePreferencial));
    }

    // Adicionar email da empresa se disponível
    if (lead.crmEmpresa?.email) {
      fields.EMAIL = [{
        VALUE: lead.crmEmpresa.email,
        VALUE_TYPE: 'WORK'
      }];
      console.log('BITRIX: Email adicionado ao contato:', lead.crmEmpresa.email);
    }

    // Comentários básicos sobre o contato
    const comentarios = `Responsável da empresa: ${lead.empresa}\nOrigem: ${lead.origem}`;
    fields.COMMENTS = comentarios;

    return {
      fields,
      params: {
        REGISTER_SONET_EVENT: 'Y' // Notificar responsável
      }
    };
  }

  /**
   * Converte um Lead do nosso sistema para o formato do Bitrix
   */
  private converterLeadParaBitrix(lead: Lead, contactId?: number): BitrixLeadPayload {
    // Extrair nome e sobrenome
    const nomeResponsavel = lead.nomeResponsavel || '';
    const partesNome = nomeResponsavel.trim().split(' ');
    const nome = partesNome[0] || '';
    const sobrenome = partesNome.slice(1).join(' ') || '';

    // Montar comentários com informações extras
    const comentarios = this.montarComentarios(lead);

    // Determinar fonte baseada na origem
    const fonte = this.mapearOrigem(lead.origem);

    const empresa = lead.empresa || 'Empresa sem nome';
    const titulo = `${empresa} - ${nomeResponsavel}`;

    const fields: BitrixLeadFields = {
      TITLE: titulo,
      NAME: nome,
      LAST_NAME: sobrenome,
      COMPANY_TITLE: empresa,
      STATUS_ID: lead.bitrixStatusId || 'UC_4J1U7T',
      SOURCE_ID: fonte.id,
      SOURCE_DESCRIPTION: fonte.descricao,
      COMMENTS: comentarios
    };

    // Vincular contato se foi criado
    if (contactId) {
      fields.CONTACT_ID = contactId;
      console.log('BITRIX: Vinculando lead ao contato ID:', contactId);
    }

    // Adicionar telefone se disponível
    if (lead.telefone) {
      fields.PHONE = [{
        VALUE: this.formatarTelefone(lead.telefone),
        VALUE_TYPE: 'MOBILE'
      }];
    }

    // Adicionar email da empresa se disponível
    if (lead.crmEmpresa?.email) {
      fields.EMAIL = [{
        VALUE: lead.crmEmpresa.email,
        VALUE_TYPE: 'WORK'
      }];
    }

    // Adicionar links do Instagram e site
    const webs: BitrixWeb[] = [];

    if (lead.instagramHandle) {
      webs.push({
        VALUE: `https://instagram.com/${lead.instagramHandle}`,
        VALUE_TYPE: 'OTHER'
      });
    }

    if (lead.linkInsta && lead.linkInsta !== 'null' && lead.linkInsta.trim()) {
      webs.push({
        VALUE: lead.linkInsta,
        VALUE_TYPE: 'OTHER'
      });
    }

    if (lead.instagramData?.website && lead.instagramData.website !== 'null' && lead.instagramData.website.trim()) {
      webs.push({
        VALUE: lead.instagramData.website,
        VALUE_TYPE: 'WORK'
      });
    }

    if (webs.length > 0) {
      fields.WEB = webs;
    }

    // *** NOVO: Mapear custom fields dinamicamente ***
    console.log('BITRIX: Iniciando mapeamento dinâmico de custom fields...');
    const customFields = this.mapLeadToCustomFields(lead);

    // Adicionar todos os custom fields mapeados ao payload
    Object.assign(fields, customFields);

    // Manter campos legados para compatibilidade (serão sobrescritos se mapeados dinamicamente)
    if (!customFields['UF_CRM_1615222177542'] && lead.instagramHandle) {
      fields['UF_CRM_1615222177542'] = `https://instagram.com/${lead.instagramHandle}`;
    }

    if (lead.score !== undefined && lead.score !== null) {
      fields['UF_SCORE'] = lead.score.toString();
    }

    if (lead.segmento) {
      fields['UF_SEGMENTO'] = lead.segmento;
    }

    if (lead.instagramData?.followers) {
      fields['UF_SEGUIDORES'] = lead.instagramData.followers.toString();
    }

    console.log('BITRIX: Custom fields finais adicionados:', Object.keys(customFields).length, 'campos');
    console.log('BITRIX: Preview dos custom fields:', customFields);

    return {
      fields,
      params: {
        REGISTER_SONET_EVENT: 'Y' // Notificar responsável
      }
    };
  }

  /**
   * Monta comentários com informações detalhadas do lead
   */
  private montarComentarios(lead: Lead): string {
    const comentarios: string[] = [];

    // Informações básicas
    comentarios.push(`Score: ${lead.score}%`);
    comentarios.push(`Segmento: ${lead.segmento || 'Não definido'}`);
    comentarios.push(`Etapa: ${lead.etapa}`);

    // Dados do Instagram (apenas métricas básicas)
    if (lead.instagramData) {
      comentarios.push('\\n=== DADOS INSTAGRAM ===');
      if (lead.instagramData.followers) {
        comentarios.push(`Seguidores: ${lead.instagramData.followers.toLocaleString()}`);
      }
      if (lead.instagramData.following) {
        comentarios.push(`Seguindo: ${lead.instagramData.following.toLocaleString()}`);
      }
      if (lead.instagramData.accountType) {
        comentarios.push(`Tipo de conta: ${lead.instagramData.accountType}`);
      }
      if (lead.instagramData.businessCategory) {
        comentarios.push(`Categoria: ${lead.instagramData.businessCategory}`);
      }
    }

    // Observações
    if (lead.observacoes) {
      comentarios.push('\\n=== OBSERVAÇÕES DE VENDAS ===');
      comentarios.push(lead.observacoes);
    }

    const comentarioCompleto = comentarios.join('\\n');

    // Limitar tamanho do comentário para evitar erro 400 do Bitrix
    const LIMITE_CARACTERES = 900; // Limite conservador (Bitrix aceita máximo 1000)
    if (comentarioCompleto.length > LIMITE_CARACTERES) {
      console.log(`BITRIX: Comentário muito longo (${comentarioCompleto.length} chars), truncando para ${LIMITE_CARACTERES}`);
      return comentarioCompleto.substring(0, LIMITE_CARACTERES) + '\\n\\n[...TEXTO TRUNCADO...]';
    }

    return comentarioCompleto;
  }

  /**
   * Mapeia origem do lead para fonte do Bitrix
   */
  private mapearOrigem(origem: string): { id: string; descricao: string } {
    const mapeamento: Record<string, { id: string; descricao: string }> = {
      'Instagram': { id: 'WEB', descricao: 'Lead gerado via Instagram' },
      'Site/Landing Page': { id: 'WEB', descricao: 'Lead do site/landing page' },
      'WhatsApp Direto': { id: 'OTHER', descricao: 'Contato direto via WhatsApp' },
      'Indicação': { id: 'PARTNER', descricao: 'Lead por indicação' },
      'Evento/Feira': { id: 'TRADE_SHOW', descricao: 'Lead de evento/feira' },
      'Outros': { id: 'OTHER', descricao: 'Outras fontes' }
    };

    return mapeamento[origem] || { id: 'OTHER', descricao: origem };
  }

  /**
   * Mapeia etapa do funil para status do Bitrix
   */
  private mapearEtapaParaStatus(etapa: string): string {
    const mapeamento: Record<string, string> = {
      'Prospecção': 'NEW',
      'Qualificação': 'IN_PROCESS',
      'Objeção': 'IN_PROCESS',
      'Fechamento': 'PROCESSED',
      'Ganho': 'CONVERTED',
      'Perdido': 'JUNK'
    };

    return mapeamento[etapa] || 'NEW';
  }

  /**
   * Obter telefone preferencial do lead (WhatsApp > Celular > Principal)
   */
  private obterTelefonePreferencial(lead: Lead): string | null {
    // Prioridade 1: WhatsApp
    const telefoneWhatsApp = lead.getTelefoneWhatsApp?.();
    if (telefoneWhatsApp?.numero) {
      console.log('BITRIX: Usando telefone WhatsApp:', telefoneWhatsApp.numero);
      return telefoneWhatsApp.numero;
    }

    // Prioridade 2: Celular
    const telefoneCelular = lead.getTelefoneCelular?.();
    if (telefoneCelular?.numero) {
      console.log('BITRIX: Usando telefone celular:', telefoneCelular.numero);
      return telefoneCelular.numero;
    }

    // Prioridade 3: Telefone principal
    if (lead.telefone) {
      console.log('BITRIX: Usando telefone principal:', lead.telefone);
      return lead.telefone;
    }

    console.warn('BITRIX: Nenhum telefone encontrado no lead');
    return null;
  }

  /**
   * Formata telefone para o padrão internacional
   */
  private formatarTelefone(telefone: string): string {
    const numeroLimpo = telefone.replace(/\D/g, '');

    if (numeroLimpo.startsWith('55')) {
      return `+${numeroLimpo}`;
    }

    return `+55${numeroLimpo}`;
  }


  /**
   * Busca um lead completo no Bitrix24 pelo ID
   */
  async buscarLeadCompleto(leadId: number): Promise<Resposta<any>> {
    try {
      console.log('BITRIX: Buscando lead completo por ID:', leadId);

      if (!leadId || leadId <= 0) {
        return Resposta.erro('ID do lead deve ser um número positivo') as any;
      }

      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.lead.get.json`;

      const payload = {
        id: leadId
      };

      console.log('BITRIX: URL:', url);
      console.log('BITRIX: Payload:', payload);

      const response = await axios.post(url, payload, {
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'PromoKit-CRM/1.0'
        },
        timeout: 30000 // 30 segundos
      });

      // Log da resposta completa do Bitrix
      console.log('BITRIX: Resposta completa do Bitrix24:', JSON.stringify(response.data, null, 2));
      console.log('BITRIX: Status da resposta:', response.status);
      console.log('BITRIX: Headers da resposta:', JSON.stringify(response.headers, null, 2));

      if (response.data.error) {
        console.error('BITRIX: Erro ao buscar lead:', response.data.error);

        // Tratar erro específico de lead não encontrado
        if (response.data.error === 'NOT_FOUND' || response.data.error_description?.includes('not found')) {
          return Resposta.erro(`Lead com ID ${leadId} não encontrado no Bitrix24`) as any;
        }

        return Resposta.erro(`Erro do Bitrix: ${response.data.error_description || response.data.error}`) as any;
      }

      if (!response.data.result) {
        return Resposta.erro(`Lead com ID ${leadId} não encontrado`) as any;
      }

      const leadData = response.data.result;
      console.log('BITRIX: Lead encontrado:', leadData.TITLE || leadData.NAME);

      // Formatar dados do lead para resposta estruturada
      const leadFormatado = {
        // Dados básicos
        id: leadData.ID,
        titulo: leadData.TITLE,
        nome: leadData.NAME || leadData.TITLE,
        sobrenome: leadData.LAST_NAME,
        nomeCompleto: this.construirNomeCompleto(leadData.NAME, leadData.LAST_NAME),
        empresa: leadData.COMPANY_TITLE,

        // Contatos
        telefones: this.formatarTelefones(leadData.PHONE),
        emails: this.formatarEmails(leadData.EMAIL),
        website: this.formatarWebsites(leadData.WEB),

        // Status e classificação
        status: {
          id: leadData.STATUS_ID,
          nome: this.obterNomeStatus(leadData.STATUS_ID)
        },
        origem: {
          id: leadData.SOURCE_ID,
          nome: this.obterNomeOrigem(leadData.SOURCE_ID)
        },

        // Responsável
        responsavel: {
          id: leadData.ASSIGNED_BY_ID,
          nome: leadData.ASSIGNED_BY_NAME || 'Não informado'
        },

        // Datas
        dataCriacao: leadData.DATE_CREATE,
        dataModificacao: leadData.DATE_MODIFY,
        dataFechamento: leadData.DATE_CLOSED,

        // Informações comerciais
        oportunidade: leadData.OPPORTUNITY ? parseFloat(leadData.OPPORTUNITY) : 0,
        moeda: leadData.CURRENCY_ID,

        // Observações e comentários
        observacoes: leadData.COMMENTS,

        // Endereço
        endereco: this.formatarEndereco(leadData),

        // Campos customizados (se houver)
        camposCustomizados: this.extrairCamposCustomizados(leadData),

        // Metadados
        metadata: {
          criadoPor: leadData.CREATED_BY_ID,
          modificadoPor: leadData.MODIFY_BY_ID,
          aberto: leadData.OPENED === 'Y',
          leadId: leadData.LEAD_ID,
          contatoId: leadData.CONTACT_ID,
          empresaId: leadData.COMPANY_ID
        }
      };

      console.log('BITRIX: Lead formatado com sucesso');
      return Resposta.sucesso(leadFormatado) as Resposta<any>;

    } catch (error) {
      console.error('BITRIX: Erro ao buscar lead completo:', error);

      if (error.code === 'ECONNABORTED') {
        return Resposta.erro('Timeout ao buscar lead no Bitrix24. Tente novamente.') as any;
      }

      return Resposta.erro(`Erro ao buscar lead completo: ${error.message}`) as any;
    }
  }

  /**
   * Busca um contato específico do Bitrix24 pelo ID
   * @param contactId ID do contato
   * @returns Dados completos do contato
   */
  async buscarContatoPorId(contactId: number): Promise<Resposta<any>> {
    try {
      console.log('BITRIX: Buscando contato por ID:', contactId);

      if (!contactId || contactId <= 0) {
        return Resposta.erro('ID do contato deve ser um número positivo') as any;
      }

      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.contact.get.json`;

      const payload = {
        id: contactId
      };

      console.log('BITRIX: URL:', url);
      console.log('BITRIX: Payload:', JSON.stringify(payload, null, 2));

      const response = await axios.post(url, payload, {
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'PromoKit-CRM/1.0'
        },
        timeout: 30000 // 30 segundos
      });

      console.log('BITRIX: Resposta do contato:', JSON.stringify(response.data, null, 2));

      if (response.data.error) {
        console.error('BITRIX: Erro ao buscar contato:', response.data.error);

        // Tratar erro específico de contato não encontrado
        if (response.data.error === 'NOT_FOUND' || response.data.error_description?.includes('not found')) {
          return Resposta.erro(`Contato com ID ${contactId} não encontrado no Bitrix24`) as any;
        }

        return Resposta.erro(`Erro do Bitrix: ${response.data.error_description || response.data.error}`) as any;
      }

      if (!response.data.result) {
        return Resposta.erro(`Contato com ID ${contactId} não encontrado`) as any;
      }

      const contatoData = response.data.result;
      console.log('BITRIX: Contato encontrado:', contatoData.NAME || contatoData.LAST_NAME);

      // Formatar dados do contato para resposta estruturada
      const contatoFormatado = {
        // Dados básicos
        id: contatoData.ID,
        nome: contatoData.NAME,
        sobrenome: contatoData.LAST_NAME,
        segundoNome: contatoData.SECOND_NAME,
        nomeCompleto: this.construirNomeCompleto(contatoData.NAME, contatoData.LAST_NAME, contatoData.SECOND_NAME),
        cargo: contatoData.POST,

        // Contatos
        telefones: this.formatarTelefones(contatoData.PHONE),
        emails: this.formatarEmails(contatoData.EMAIL),
        website: this.formatarWebsites(contatoData.WEB),

        // Empresa associada
        empresa: {
          id: contatoData.COMPANY_ID,
          nome: contatoData.COMPANY_TITLE
        },

        // Classificação
        tipo: {
          id: contatoData.TYPE_ID,
          nome: this.obterNomeTipoContato(contatoData.TYPE_ID)
        },
        origem: {
          id: contatoData.SOURCE_ID,
          nome: this.obterNomeOrigem(contatoData.SOURCE_ID)
        },

        // Responsável
        responsavel: {
          id: contatoData.ASSIGNED_BY_ID,
          nome: contatoData.ASSIGNED_BY_NAME || 'Não informado'
        },

        // Datas
        dataCriacao: contatoData.DATE_CREATE,
        dataModificacao: contatoData.DATE_MODIFY,
        dataNascimento: contatoData.BIRTHDATE,

        // Observações
        observacoes: contatoData.COMMENTS,

        // Endereço
        endereco: this.formatarEndereco(contatoData),

        // Campos customizados (se houver)
        camposCustomizados: this.extrairCamposCustomizados(contatoData),

        // Metadados
        metadata: {
          criadoPor: contatoData.CREATED_BY_ID,
          modificadoPor: contatoData.MODIFY_BY_ID,
          aberto: contatoData.OPENED === 'Y',
          exportado: contatoData.EXPORT === 'Y'
        }
      };

      console.log('BITRIX: Contato formatado com sucesso');
      return Resposta.sucesso(contatoFormatado) as Resposta<any>;

    } catch (error) {
      console.error('BITRIX: Erro ao buscar contato:', error);

      if (error.code === 'ECONNABORTED') {
        return Resposta.erro('Timeout ao buscar contato no Bitrix24. Tente novamente.') as any;
      }

      return Resposta.erro(`Erro ao buscar contato: ${error.message}`) as any;
    }
  }

  /**
   * Busca uma empresa específica do Bitrix24 pelo ID
   * @param companyId ID da empresa
   * @returns Dados completos da empresa
   */
  async buscarEmpresaPorId(companyId: number): Promise<Resposta<any>> {
    try {
      console.log('BITRIX: Buscando empresa por ID:', companyId);

      if (!companyId || companyId <= 0) {
        return Resposta.erro('ID da empresa deve ser um número positivo') as any;
      }

      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.company.get.json`;

      const payload = {
        id: companyId
      };

      console.log('BITRIX: URL:', url);
      console.log('BITRIX: Payload:', JSON.stringify(payload, null, 2));

      const response = await axios.post(url, payload, {
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'PromoKit-CRM/1.0'
        },
        timeout: 30000 // 30 segundos
      });

      console.log('BITRIX: Resposta da empresa:', JSON.stringify(response.data, null, 2));

      if (response.data.error) {
        console.error('BITRIX: Erro ao buscar empresa:', response.data.error);

        // Tratar erro específico de empresa não encontrada
        if (response.data.error === 'NOT_FOUND' || response.data.error_description?.includes('not found')) {
          return Resposta.erro(`Empresa com ID ${companyId} não encontrada no Bitrix24`) as any;
        }

        return Resposta.erro(`Erro do Bitrix: ${response.data.error_description || response.data.error}`) as any;
      }

      if (!response.data.result) {
        return Resposta.erro(`Empresa com ID ${companyId} não encontrada`) as any;
      }

      const empresaData = response.data.result;
      console.log('BITRIX: Empresa encontrada:', empresaData.TITLE);

      // Formatar dados da empresa para resposta estruturada
      const empresaFormatada = {
        // Dados básicos
        id: empresaData.ID,
        titulo: empresaData.TITLE,
        nomeFantasia: empresaData.TITLE,
        razaoSocial: empresaData.COMPANY_TYPE,

        // Contatos
        telefones: this.formatarTelefones(empresaData.PHONE),
        emails: this.formatarEmails(empresaData.EMAIL),
        website: this.formatarWebsites(empresaData.WEB),

        // Classificação
        tipo: {
          id: empresaData.COMPANY_TYPE,
          nome: this.obterNomeTipoEmpresa(empresaData.COMPANY_TYPE)
        },
        setor: {
          id: empresaData.INDUSTRY,
          nome: this.obterNomeSetor(empresaData.INDUSTRY)
        },

        // Responsável
        responsavel: {
          id: empresaData.ASSIGNED_BY_ID,
          nome: empresaData.ASSIGNED_BY_NAME || 'Não informado'
        },

        // Datas
        dataCriacao: empresaData.DATE_CREATE,
        dataModificacao: empresaData.DATE_MODIFY,

        // Informações comerciais
        receita: empresaData.REVENUE ? parseFloat(empresaData.REVENUE) : 0,
        moeda: empresaData.CURRENCY_ID,
        numeroFuncionarios: empresaData.EMPLOYEES,

        // Observações
        observacoes: empresaData.COMMENTS,

        // Endereço
        endereco: this.formatarEndereco(empresaData),

        // Campos customizados (se houver)
        camposCustomizados: this.extrairCamposCustomizados(empresaData),

        // Metadados
        metadata: {
          criadoPor: empresaData.CREATED_BY_ID,
          modificadoPor: empresaData.MODIFY_BY_ID,
          aberto: empresaData.OPENED === 'Y',
          exportado: empresaData.EXPORT === 'Y'
        }
      };

      console.log('BITRIX: Empresa formatada com sucesso');
      return Resposta.sucesso(empresaFormatada) as Resposta<any>;

    } catch (error) {
      console.error('BITRIX: Erro ao buscar empresa:', error);

      if (error.code === 'ECONNABORTED') {
        return Resposta.erro('Timeout ao buscar empresa no Bitrix24. Tente novamente.') as any;
      }

      return Resposta.erro(`Erro ao buscar empresa: ${error.message}`) as any;
    }
  }

  /**
   * Busca um lead no Bitrix pelo ID (método legado - mantido para compatibilidade)
   */
  async buscarLead(leadId: number): Promise<Resposta<any>> {
    try {
      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.lead.get.json?id=${leadId}`;

      const response = await axios.get(url);

      if (response.data.error) {
        return Resposta.erro(`Erro do Bitrix: ${response.data.error_description}`);
      }

      return Resposta.sucesso(response.data.result) as Resposta<any>;
    } catch (error) {
      return Resposta.erro(`Erro ao buscar lead: ${error.message}`);
    }
  }

  /**
   * Atualiza um lead no Bitrix
   */
  async atualizarLead(leadId: number, campos: Partial<BitrixLeadFields>): Promise<Resposta<boolean>> {
    try {
      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.lead.update.json`;

      const payload = {
        id: leadId,
        fields: campos
      };

      const response = await axios.post(url, payload);

      if (response.data.error) {
        return Resposta.erro(`Erro do Bitrix: ${response.data.error_description}`) as any;
      }

      return Resposta.sucesso(true) as Resposta<boolean>;
    } catch (error) {
      return Resposta.erro(`Erro ao atualizar lead: ${error.message}`) as any;
    }
  }

  /**
   * Busca leads no Bitrix24 por telefone
   */
  async buscarLeads(query: {
    filter?: Record<string, any>;
    select?: string[];
    start?: number;
    limit?: number;
  }): Promise<Resposta<any[]>> {
    try {
      console.log('BITRIX: Buscando leads com query:', query);

      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.lead.list.json`;

      const defaultSelect = [
        'ID', 'TITLE', 'NAME', 'LAST_NAME', 'COMPANY_TITLE',
        'PHONE', 'EMAIL', 'STATUS_ID', 'SOURCE_ID', 'ASSIGNED_BY_ID',
        'DATE_CREATE', 'DATE_MODIFY', 'COMMENTS', 'OPPORTUNITY'
      ];

      const payload = {
        filter: query.filter || {},
        select: query.select || defaultSelect,
        start: query.start || 0,
        ...(query.limit && { limit: query.limit })
      };

      console.log('BITRIX: Fazendo chamada para:', url);
      console.log('BITRIX: Payload:', JSON.stringify(payload, null, 2));

      const response = await axios.post(url, payload);

      if (response.data.error) {
        console.error('BITRIX: Erro na busca de leads:', response.data.error);
        return Resposta.erro(`Erro ao buscar leads: ${response.data.error.error_description || response.data.error}`) as any;
      }

      const resultados = response.data.result || [];
      console.log(`BITRIX: Total de leads encontrados: ${resultados.length}`);

      // Formatar resultados para resposta
      const leadsFormatados = resultados.map((lead: any) => ({
        id: lead.ID,
        titulo: lead.TITLE,
        nome: lead.NAME,
        sobrenome: lead.LAST_NAME,
        empresa: lead.COMPANY_TITLE,
        telefones: lead.PHONE || [],
        emails: lead.EMAIL || [],
        status: lead.STATUS_ID,
        origem: lead.SOURCE_ID,
        responsavel: lead.ASSIGNED_BY_ID,
        dataCriacao: lead.DATE_CREATE,
        dataModificacao: lead.DATE_MODIFY,
        observacoes: lead.COMMENTS,
        oportunidade: lead.OPPORTUNITY
      }));

      return Resposta.sucesso(leadsFormatados) as Resposta<any[]>;

    } catch (error) {
      console.error('BITRIX: Erro ao buscar leads:', error);
      return Resposta.erro(`Erro ao buscar leads: ${error.message}`) as any;
    }
  }

  async buscarLeadsPorTelefone(telefone: string): Promise<Resposta<any[]>> {
    try {
      console.log('BITRIX: Buscando leads por telefone:', telefone);

      // Limpar telefone removendo caracteres especiais, mas preservando o +
      let telefoneLimpo = telefone.replace(/[^\d+]/g, '');

      console.log('BITRIX SERVICE: Telefone original:', telefone);
      console.log('BITRIX SERVICE: Telefone limpo inicial:', telefoneLimpo);

      // Remover código do país brasileiro (+55 ou 55) se presente
      if (telefoneLimpo.startsWith('+55')) {
        telefoneLimpo = telefoneLimpo.substring(3); // Remove +55
        console.log('BITRIX SERVICE: Removido código do país +55:', telefoneLimpo);
      } else if (telefoneLimpo.startsWith('55') && (telefoneLimpo.length === 12 || telefoneLimpo.length === 13)) {
        telefoneLimpo = telefoneLimpo.substring(2); // Remove 55
        console.log('BITRIX SERVICE: Removido código do país 55:', telefoneLimpo);
      }

      if (telefoneLimpo.length < 10 || telefoneLimpo.length > 11) {
        return Resposta.erro(`Telefone inválido. Formato esperado: 10-11 dígitos (brasileiro) ou 12-13 dígitos (com 55). Recebido: ${telefoneLimpo.length} dígitos`) as any;
      }

      console.log('BITRIX SERVICE: Telefone normalizado para busca:', telefoneLimpo);

      // Usar filtro LIKE com % antes e depois do número (sem código do país)
      const telefoneBusca = `%${telefoneLimpo}%`;
      console.log('BITRIX: Buscando com filtro LIKE:', telefoneBusca);

      // Usar o método genérico buscarLeads
      return await this.buscarLeads({
        filter: {
          'PHONE': telefoneBusca
        }
      });

    } catch (error) {
      console.error('BITRIX: Erro ao buscar leads por telefone:', error);
      return Resposta.erro(`Erro ao buscar leads por telefone: ${error.message}`) as any;
    }
  }

  async buscarLeadsPorContatoTelefone(telefone: string): Promise<Resposta<any[]>> {
    try {
      console.log('BITRIX: Buscando leads por contato com telefone:', telefone);

      // Primeiro, buscar contatos por telefone
      const respostaContatos = await this.buscarContatosPorTelefone(telefone);

      if (!respostaContatos.sucesso) {
        return Resposta.erro(`Erro ao buscar contatos: ${respostaContatos.erro}`) as any;
      }

      const contatos = respostaContatos.data;

      if (!contatos || contatos.length === 0) {
        console.log('BITRIX: Nenhum contato encontrado para o telefone:', telefone);
        return Resposta.sucesso([]) as Resposta<any[]>;
      }

      console.log(`BITRIX: Encontrados ${contatos.length} contatos. Buscando leads associados...`);

      // Extrair IDs dos contatos encontrados
      const contactIds = contatos.map((contato: any) => contato.id).filter((id: any) => id);

      if (contactIds.length === 0) {
        console.log('BITRIX: Nenhum ID de contato válido encontrado');
        return Resposta.sucesso([]) as Resposta<any[]>;
      }

      // Buscar leads usando os IDs dos contatos
      const leadsPromises = contactIds.map((contactId: any) =>
        this.buscarLeads({
          filter: {
            'CONTACT_ID': contactId
          }
        })
      );

      const respostasLeads = await Promise.all(leadsPromises);

      // Consolidar todos os leads encontrados
      const todosLeads: any[] = [];
      for (const respostaLead of respostasLeads) {
        if (respostaLead.sucesso && respostaLead.data) {
          todosLeads.push(...respostaLead.data);
        }
      }

      console.log(`BITRIX: Total de leads encontrados via contatos: ${todosLeads.length}`);

      // Buscar dados completos de cada lead
      const leadsCompletos: any[] = [];
      for (const lead of todosLeads) {
        try {
          console.log(`BITRIX: Buscando dados completos do lead ID: ${lead.id}`);
          const respostaLeadCompleto = await this.buscarLeadCompleto(lead.id);

          if (respostaLeadCompleto.sucesso && respostaLeadCompleto.data) {
            leadsCompletos.push(respostaLeadCompleto.data);
          } else {
            console.warn(`BITRIX: Erro ao buscar lead completo ${lead.id}:`, respostaLeadCompleto.erro);
            // Em caso de erro, mantém os dados básicos
            leadsCompletos.push(lead);
          }
        } catch (error) {
          console.error(`BITRIX: Erro ao buscar lead completo ${lead.id}:`, error);
          // Em caso de erro, mantém os dados básicos
          leadsCompletos.push(lead);
        }
      }

      console.log(`BITRIX: Total de leads completos processados: ${leadsCompletos.length}`);

      return Resposta.sucesso(leadsCompletos) as Resposta<any[]>;

    } catch (error) {
      console.error('BITRIX: Erro ao buscar leads por contato com telefone:', error);
      return Resposta.erro(`Erro ao buscar leads por contato com telefone: ${error.message}`) as any;
    }
  }

  /**
   * Verifica se um número é de celular brasileiro
   */
  private isCelularBrasileiro(numero: string): boolean {
    // Remove código do país se presente
    const numeroSemCodigo = numero.replace(/^\+?55/, '');

    // Celular brasileiro: DDD (11-99) + número iniciado com 6-9
    if (numeroSemCodigo.length === 10 || numeroSemCodigo.length === 11) {
      const ddd = numeroSemCodigo.substring(0, 2);
      const primeiroDigito = numeroSemCodigo.substring(2, 3);

      // DDD válido (11-99) e primeiro dígito de celular (6-9)
      return parseInt(ddd) >= 11 && parseInt(ddd) <= 99 && ['6', '7', '8', '9'].includes(primeiroDigito);
    }

    return false;
  }

  /**
   * Extrai número sem código do país
   */
  private extrairNumeroSemCodigo(telefoneCompleto: string): string {
    return telefoneCompleto.replace(/^\+?55/, '');
  }

  /**
   * Gera versão alternativa do número (com/sem 9º dígito)
   */
  private gerarNumeroAlternativo(telefoneCompleto: string): string {
    const codigoPais = telefoneCompleto.startsWith('+55') ? '+55' : (telefoneCompleto.startsWith('55') ? '55' : '');
    const numeroSemCodigo = this.extrairNumeroSemCodigo(telefoneCompleto);

    if (numeroSemCodigo.length === 11 && numeroSemCodigo.substring(2, 3) === '9') {
      // Remove o 9º dígito
      const numeroSem9 = numeroSemCodigo.substring(0, 2) + numeroSemCodigo.substring(3);
      return codigoPais + numeroSem9;
    } else if (numeroSemCodigo.length === 10) {
      // Adiciona o 9º dígito
      const numeroCom9 = numeroSemCodigo.substring(0, 2) + '9' + numeroSemCodigo.substring(2);
      return codigoPais + numeroCom9;
    }

    return telefoneCompleto; // Retorna original se não for celular
  }

  /**
   * Busca contatos no Bitrix24 por telefone
   */
  async buscarContatosPorTelefone(telefone: string): Promise<Resposta<any[]>> {
    try {
      console.log('BITRIX: Buscando contatos por telefone:', telefone);

      // Limpar telefone removendo caracteres especiais, mas preservando o +
      let telefoneLimpo = telefone.replace(/[^\d+]/g, '');

      console.log('BITRIX SERVICE: Telefone original:', telefone);
      console.log('BITRIX SERVICE: Telefone limpo inicial:', telefoneLimpo);

      // Verificar se já tem código do país
      let jaTinhaCodigoPais = telefoneLimpo.startsWith('+');

      // Garantir que o telefone tenha o código do país com +
      if (!jaTinhaCodigoPais) {
        // Se não tem +, verificar se começa com 55 (código brasileiro)
        if (telefoneLimpo.startsWith('55') && (telefoneLimpo.length === 12 || telefoneLimpo.length === 13)) {
          telefoneLimpo = '+' + telefoneLimpo;
        } else {
          telefoneLimpo = '+55' + telefoneLimpo;
        }
        console.log('BITRIX SERVICE: Adicionado código do país:', telefoneLimpo);
      }

      console.log('BITRIX SERVICE: Telefone normalizado para busca:', telefoneLimpo);

      // Preparar números para busca
      const numerosBusca = [telefoneLimpo];

      // Se for celular brasileiro, adicionar versão alternativa (com/sem 9º dígito)
      if (this.isCelularBrasileiro(telefoneLimpo)) {
        const numeroAlternativo = this.gerarNumeroAlternativo(telefoneLimpo);
        if (numeroAlternativo !== telefoneLimpo) {
          numerosBusca.push(numeroAlternativo);
          console.log('BITRIX SERVICE: Adicionada versão alternativa para busca:', numeroAlternativo);
        }
      }

      console.log('BITRIX SERVICE: Números que serão buscados:', numerosBusca);

      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.contact.list.json`;

      // Realizar buscas para todos os números
      let todosResultados: any[] = [];
      const idsEncontrados = new Set<string>(); // Para evitar duplicatas

      for (const numeroBusca of numerosBusca) {
        const telefoneBusca = `%${numeroBusca}%`;
        console.log('BITRIX: Buscando com filtro LIKE:', telefoneBusca);

        const payload = {
          filter: {
            'PHONE': telefoneBusca
          },
          select: [
            'ID', 'NAME', 'LAST_NAME', 'SECOND_NAME', 'POST',
            'PHONE', 'EMAIL', 'COMPANY_ID', 'COMPANY_TITLE',
            'TYPE_ID', 'SOURCE_ID', 'ASSIGNED_BY_ID',
            'DATE_CREATE', 'DATE_MODIFY', 'COMMENTS', 'BIRTHDATE'
          ]
        };

        try {
          const response = await axios.post(url, payload);

          if (response.data.error) {
            console.error('BITRIX: Erro na busca por telefone:', response.data.error);
            continue; // Continua para próxima busca
          }

          const resultados = response.data.result || [];
          console.log(`BITRIX: Encontrados ${resultados.length} contatos para ${numeroBusca}`);

          // Adicionar apenas contatos únicos (por ID)
          resultados.forEach((contato: any) => {
            if (!idsEncontrados.has(contato.ID)) {
              idsEncontrados.add(contato.ID);
              todosResultados.push(contato);
            }
          });

        } catch (erro) {
          console.error(`BITRIX: Erro ao buscar ${numeroBusca}:`, erro);
          continue; // Continua para próxima busca
        }
      }

      console.log(`BITRIX: Total de contatos únicos encontrados: ${todosResultados.length}`);

      // Formatar resultados para resposta
      const contatosFormatados = todosResultados.map((contato: any) => ({
        id: contato.ID,
        nome: contato.NAME,
        sobrenome: contato.LAST_NAME,
        segundoNome: contato.SECOND_NAME,
        cargo: contato.POST,
        telefones: contato.PHONE || [],
        emails: contato.EMAIL || [],
        empresaId: contato.COMPANY_ID,
        empresaNome: contato.COMPANY_TITLE,
        tipo: contato.TYPE_ID,
        origem: contato.SOURCE_ID,
        responsavel: contato.ASSIGNED_BY_ID,
        dataCriacao: contato.DATE_CREATE,
        dataModificacao: contato.DATE_MODIFY,
        observacoes: contato.COMMENTS,
        dataNascimento: contato.BIRTHDATE
      }));

      return Resposta.sucesso(contatosFormatados) as Resposta<any[]>;

    } catch (error) {
      console.error('BITRIX: Erro ao buscar contatos por telefone:', error);
      return Resposta.erro(`Erro ao buscar contatos por telefone: ${error.message}`) as any;
    }
  }

  /**
   * Lista os custom fields (campos personalizados) de leads do Bitrix
   */
  async listarCustomFields(): Promise<Resposta<any[]>> {
    try {
      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.lead.userfield.list.json`;

      console.log('BITRIX: Listando custom fields de leads...');
      console.log('BITRIX: URL:', url);

      const response = await axios.get(url);

      if (response.data.error) {
        console.error('BITRIX: Erro ao listar custom fields:', response.data);
        return Resposta.erro(`Erro do Bitrix: ${response.data.error_description}`) as any;
      }

      const customFields = response.data.result;
      console.log('BITRIX: Custom fields raw data:', JSON.stringify(customFields, null, 2));
      console.log('BITRIX: Custom fields encontrados:', customFields.length);

      // Processar campos para exibir informações mais úteis
      const camposProcessados = customFields.map((field: any) => ({
        id: field.ID,
        fieldName: field.FIELD_NAME,
        userTypeId: field.USER_TYPE_ID,
        listLabel: field.LIST_LABEL,
        listColumnLabel: field.LIST_COLUMN_LABEL,
        listFilterLabel: field.LIST_FILTER_LABEL,
        editFormLabel: field.EDIT_FORM_LABEL,
        settings: field.SETTINGS,
        mandatory: field.MANDATORY === 'Y',
        multiple: field.MULTIPLE === 'Y',
        sort: field.SORT,
        showInList: field.SHOW_IN_LIST === 'Y',
        editInList: field.EDIT_IN_LIST === 'Y',
        showFilter: field.SHOW_FILTER === 'Y'
      }));

      console.log('BITRIX: Campos processados:', JSON.stringify(camposProcessados, null, 2));

      return Resposta.sucesso(camposProcessados) as Resposta<any[]>;
    } catch (error) {
      console.error('BITRIX: Erro na requisição de custom fields:', error);
      return Resposta.erro(`Erro ao listar custom fields: ${error.message}`) as any;
    }
  }

  /**
   * Busca um custom field específico do Bitrix pelo ID
   */
  async buscarCustomField(fieldId: string): Promise<Resposta<any>> {
    try {
      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.lead.userfield.get.json?id=${fieldId}`;

      console.log('BITRIX: Buscando custom field ID:', fieldId);
      console.log('BITRIX: URL:', url);

      const response = await axios.get(url);

      if (response.data.error) {
        console.error('BITRIX: Erro ao buscar custom field:', response.data);
        return Resposta.erro(`Erro do Bitrix: ${response.data.error_description}`);
      }

      const customField = response.data.result;
      console.log('BITRIX: Custom field raw data:', JSON.stringify(customField, null, 2));

      if (!customField) {
        return Resposta.erro(`Custom field com ID ${fieldId} não encontrado`);
      }

      // Processar campo para exibir informações mais úteis
      const campoProcessado = {
        id: customField.ID,
        fieldName: customField.FIELD_NAME,
        userTypeId: customField.USER_TYPE_ID,
        xmlId: customField.XML_ID,
        listLabel: customField.LIST_LABEL,
        listColumnLabel: customField.LIST_COLUMN_LABEL,
        listFilterLabel: customField.LIST_FILTER_LABEL,
        editFormLabel: customField.EDIT_FORM_LABEL,
        errorMessage: customField.ERROR_MESSAGE,
        helpMessage: customField.HELP_MESSAGE,
        settings: customField.SETTINGS,
        mandatory: customField.MANDATORY === 'Y',
        multiple: customField.MULTIPLE === 'Y',
        sort: customField.SORT,
        showInList: customField.SHOW_IN_LIST === 'Y',
        editInList: customField.EDIT_IN_LIST === 'Y',
        showFilter: customField.SHOW_FILTER === 'Y',
        showAdvancedFilter: customField.SHOW_ADV_FILTER === 'Y',
        entityId: customField.ENTITY_ID,
        dateCreate: customField.DATE_CREATE,
        dateModify: customField.DATE_MODIFY,
        createdBy: customField.CREATED_BY,
        modifiedBy: customField.MODIFIED_BY
      };

      console.log('BITRIX: Campo processado:', JSON.stringify(campoProcessado, null, 2));

      return Resposta.sucesso(campoProcessado) as Resposta<any>;
    } catch (error) {
      console.error('BITRIX: Erro na requisição do custom field:', error);
      return Resposta.erro(`Erro ao buscar custom field: ${error.message}`);
    }
  }

  /**
   * Lista custom fields com detalhes completos (combina list + get para cada campo)
   */
  async listarCustomFieldsCompletos(): Promise<Resposta<any[]>> {
    try {
      console.log('BITRIX: Buscando custom fields completos...');

      // Primeiro, listar todos os custom fields
      const resultadoLista = await this.listarCustomFields();

      if (!resultadoLista.sucesso) {
        return resultadoLista;
      }

      const customFields = resultadoLista.data;
      console.log('BITRIX: Obtendo detalhes para', customFields.length, 'custom fields...');

      const camposCompletos = [];

      // Para cada campo, buscar os detalhes completos
      for (const campo of customFields) {
        try {
          console.log('BITRIX: Buscando detalhes do campo ID:', campo.id);

          const resultadoDetalhe = await this.buscarCustomField(campo.id);

          if (resultadoDetalhe.sucesso) {
            // Combinar dados da lista com detalhes completos
            const campoCompleto = {
              id: campo.id,
              fieldName: campo.fieldName,
              userTypeId: campo.userTypeId,
              label: this.extrairLabelPortugues(resultadoDetalhe.data.listColumnLabel) ||
                     this.extrairLabelPortugues(resultadoDetalhe.data.editFormLabel) ||
                     this.extrairLabelPortugues(resultadoDetalhe.data.listFilterLabel) ||
                     campo.fieldName,
              mandatory: resultadoDetalhe.data.mandatory,
              multiple: resultadoDetalhe.data.multiple,
              showInList: resultadoDetalhe.data.showInList,
              showFilter: resultadoDetalhe.data.showFilter,
              sort: resultadoDetalhe.data.sort,
              settings: resultadoDetalhe.data.settings
            };

            camposCompletos.push(campoCompleto);
          } else {
            console.warn('BITRIX: Erro ao buscar detalhes do campo', campo.id, ':', resultadoDetalhe.erro);
            // Manter o campo mesmo sem detalhes completos
            camposCompletos.push(campo);
          }

          // Pequena pausa para evitar rate limiting
          await new Promise(resolve => setTimeout(resolve, 100));

        } catch (error) {
          console.error('BITRIX: Erro ao processar campo', campo.id, ':', error);
          // Manter o campo mesmo com erro
          camposCompletos.push(campo);
        }
      }

      console.log('BITRIX: Custom fields completos processados:', camposCompletos.length);

      return Resposta.sucesso(camposCompletos) as Resposta<any[]>;

    } catch (error) {
      console.error('BITRIX: Erro ao buscar custom fields completos:', error);
      return Resposta.erro(`Erro ao buscar custom fields completos: ${error.message}`) as any;
    }
  }

  /**
   * Extrai label em português de um objeto de labels multilíngue
   */
  private extrairLabelPortugues(labelObj: any): string {
    if (!labelObj) return '';

    if (typeof labelObj === 'string') {
      return labelObj;
    }

    if (typeof labelObj === 'object') {
      // Tentar português brasileiro primeiro, depois português, depois qualquer chave
      return labelObj.br || labelObj.pt || labelObj.en || labelObj[Object.keys(labelObj)[0]] || '';
    }

    return String(labelObj);
  }

  /**
   * Retorna o mapeamento estático dos custom fields (não precisa buscar do Bitrix)
   */
  private getCustomFieldsMapping(): Map<string, string> {
    if (this.customFieldsMappingCache.size === 0) {
      // Criar cache baseado na configuração estática
      this.CUSTOM_FIELD_MAPPINGS.forEach(mapping => {
        this.customFieldsMappingCache.set(mapping.label, mapping.fieldName);
      });
      console.log('BITRIX: Cache de mapeamento criado com', this.customFieldsMappingCache.size, 'campos');
    }

    return this.customFieldsMappingCache;
  }

  /**
   * Obtém valor de uma propriedade aninhada do objeto Lead ou chama método
   */
  private getNestedProperty(obj: any, path: string): any {
    // Se o path é um método (não contém ponto), tenta chamar como método
    if (!path.includes('.') && typeof obj[path] === 'function') {
      try {
        return obj[path]();
      } catch (error) {
        console.warn(`BITRIX: Erro ao chamar método ${path}:`, error);
        return undefined;
      }
    }

    // Caso contrário, navega pelas propriedades aninhadas
    return path.split('.').reduce((current, prop) => {
      return current && current[prop] !== undefined ? current[prop] : undefined;
    }, obj);
  }

  /**
   * Mapeia dados do Lead para custom fields do Bitrix
   */
  private mapLeadToCustomFields(lead: Lead): Record<string, any> {
    const customFields: Record<string, any> = {};

    // Buscar mapeamento dos custom fields (agora é síncrono)
    this.getCustomFieldsMapping();

    console.log('BITRIX: Iniciando mapeamento de custom fields para lead:', lead.nomeResponsavel);
    console.log('BITRIX: Lead possui', lead.links?.length || 0, 'links associados');

    // Log detalhado dos links se existirem
    if (lead.links && lead.links.length > 0) {
      console.log('BITRIX: Links detalhados:');
      lead.links.forEach((link: any, index: number) => {
        console.log(`BITRIX: Link ${index + 1}: ${link.tipo} = ${link.url} (ativo: ${link.ativo})`);
      });
    }

    // Testar métodos do Lead
    console.log('BITRIX: Testando métodos do Lead:');
    if (typeof lead.getLinkInstagram === 'function') {
      console.log('BITRIX: getLinkInstagram():', lead.getLinkInstagram());
    } else {
      console.log('BITRIX: getLinkInstagram() não é uma função');
    }

    if (typeof lead.getLinkIfood === 'function') {
      console.log('BITRIX: getLinkIfood():', lead.getLinkIfood());
    } else {
      console.log('BITRIX: getLinkIfood() não é uma função');
    }

    if (typeof lead.getLinkConcorrente === 'function') {
      console.log('BITRIX: getLinkConcorrente():', lead.getLinkConcorrente());
    } else {
      console.log('BITRIX: getLinkConcorrente() não é uma função');
    }

    // Processar cada mapeamento configurado
    for (const mapping of this.CUSTOM_FIELD_MAPPINGS) {
      if (!mapping.fieldName) {
        console.warn(`BITRIX: FieldName não encontrado para "${mapping.label}"`);
        continue;
      }

      // Obter valor da propriedade do lead
      let value = this.getNestedProperty(lead, mapping.leadProperty);

      console.log(`BITRIX: Tentando mapear ${mapping.label} (${mapping.leadProperty}): ${value}`);

      if (value !== undefined && value !== null && value !== '') {
        // Aplicar transformação se definida
        if (mapping.transform) {
          const originalValue = value;
          value = mapping.transform(value);
          console.log(`BITRIX: Transformação aplicada para ${mapping.label}: ${originalValue} -> ${value}`);
        }

        // Validação extra para evitar valores problemáticos
        if (Array.isArray(value)) {
          // Filtrar valores inválidos de arrays
          value = value.filter(item => item && item !== 'null' && String(item).trim() !== '');
          if (value.length === 0) {
            console.log(`BITRIX: ✗ Array vazio após filtrar valores inválidos para ${mapping.label}`);
            continue;
          }
        } else if (String(value) === 'null' || String(value).trim() === '') {
          console.log(`BITRIX: ✗ Valor inválido ('null' ou vazio) para ${mapping.label}`);
          continue;
        }

        customFields[mapping.fieldName] = value;
        console.log(`BITRIX: ✓ Custom field mapeado: ${mapping.fieldName} (${mapping.label}) = ${JSON.stringify(value)}`);
      } else {
        console.log(`BITRIX: ✗ Valor vazio para ${mapping.label} (${mapping.leadProperty})`);
      }
    }

    console.log('BITRIX: Total de custom fields mapeados:', Object.keys(customFields).length);
    console.log('BITRIX: Resumo dos campos enviados:', Object.keys(customFields).map(key => {
      const mapping = this.CUSTOM_FIELD_MAPPINGS.find(m => m.fieldName === key);
      return `${key} (${mapping?.label || 'Desconhecido'}): ${customFields[key]}`;
    }));

    return customFields;
  }

  /**
   * Constrói nome completo a partir de nome e sobrenome
   */
  private construirNomeCompleto(nome: string, sobrenome: string): string {
    const partes = [];
    if (nome) partes.push(nome);
    if (sobrenome) partes.push(sobrenome);
    return partes.join(' ') || 'Não informado';
  }

  /**
   * Formata array de telefones do Bitrix24
   */
  private formatarTelefones(telefones: any[]): any[] {
    if (!telefones || !Array.isArray(telefones)) return [];

    return telefones.map(tel => ({
      numero: tel.VALUE,
      tipo: this.obterTipoTelefone(tel.VALUE_TYPE),
      principal: tel.VALUE_TYPE === 'WORK'
    }));
  }

  /**
   * Formata array de emails do Bitrix24
   */
  private formatarEmails(emails: any[]): any[] {
    if (!emails || !Array.isArray(emails)) return [];

    return emails.map(email => ({
      endereco: email.VALUE,
      tipo: this.obterTipoEmail(email.VALUE_TYPE),
      principal: email.VALUE_TYPE === 'WORK'
    }));
  }

  /**
   * Formata array de websites do Bitrix24
   */
  private formatarWebsites(websites: any[]): any[] {
    if (!websites || !Array.isArray(websites)) return [];

    return websites.map(web => ({
      url: web.VALUE,
      tipo: this.obterTipoWebsite(web.VALUE_TYPE),
      principal: web.VALUE_TYPE === 'WORK'
    }));
  }

  /**
   * Formata endereço do lead
   */
  private formatarEndereco(leadData: any): any {
    return {
      rua: leadData.ADDRESS,
      cidade: leadData.ADDRESS_CITY,
      estado: leadData.ADDRESS_REGION,
      cep: leadData.ADDRESS_POSTAL_CODE,
      pais: leadData.ADDRESS_COUNTRY,
      enderecoCompleto: this.construirEnderecoCompleto(leadData)
    };
  }

  /**
   * Constrói endereço completo formatado
   */
  private construirEnderecoCompleto(leadData: any): string {
    const partes = [];
    if (leadData.ADDRESS) partes.push(leadData.ADDRESS);
    if (leadData.ADDRESS_CITY) partes.push(leadData.ADDRESS_CITY);
    if (leadData.ADDRESS_REGION) partes.push(leadData.ADDRESS_REGION);
    if (leadData.ADDRESS_POSTAL_CODE) partes.push(`CEP: ${leadData.ADDRESS_POSTAL_CODE}`);
    if (leadData.ADDRESS_COUNTRY) partes.push(leadData.ADDRESS_COUNTRY);

    return partes.length > 0 ? partes.join(', ') : '';
  }

  /**
   * Extrai campos customizados do lead
   */
  private extrairCamposCustomizados(leadData: any): any {
    const camposCustomizados: any = {};

    // Procurar por campos que começam com UF_ (User Fields)
    Object.keys(leadData).forEach(key => {
      if (key.startsWith('UF_')) {
        const valor = leadData[key];
        if (valor !== null && valor !== undefined && valor !== '') {
          camposCustomizados[key] = valor;
        }
      }
    });

    return camposCustomizados;
  }

  /**
   * Obtém nome amigável do tipo de telefone
   */
  private obterTipoTelefone(tipo: string): string {
    const tipos: { [key: string]: string } = {
      'WORK': 'Comercial',
      'MOBILE': 'Celular',
      'HOME': 'Residencial',
      'OTHER': 'Outro'
    };
    return tipos[tipo] || tipo || 'Não especificado';
  }

  /**
   * Obtém nome amigável do tipo de email
   */
  private obterTipoEmail(tipo: string): string {
    const tipos: { [key: string]: string } = {
      'WORK': 'Comercial',
      'HOME': 'Pessoal',
      'OTHER': 'Outro'
    };
    return tipos[tipo] || tipo || 'Não especificado';
  }

  /**
   * Obtém nome amigável do tipo de website
   */
  private obterTipoWebsite(tipo: string): string {
    const tipos: { [key: string]: string } = {
      'WORK': 'Site Comercial',
      'HOME': 'Site Pessoal',
      'OTHER': 'Outro Site'
    };
    return tipos[tipo] || tipo || 'Não especificado';
  }

  /**
   * Obtém nome amigável do status do lead
   */
  private obterNomeStatus(statusId: string): string {
    const status: { [key: string]: string } = {
      'NEW': 'Novo',
      'IN_PROCESS': 'Em Processo',
      'PROCESSED': 'Processado',
      'CONVERTED': 'Convertido',
      'JUNK': 'Descartado'
    };
    return status[statusId] || statusId || 'Não especificado';
  }

  /**
   * Obtém nome amigável da origem do lead
   */
  private obterNomeOrigem(origemId: string): string {
    const origens: { [key: string]: string } = {
      'CALL': 'Chamada Telefônica',
      'EMAIL': 'Email',
      'WEB': 'Website',
      'ADVERTISING': 'Publicidade',
      'PARTNER': 'Parceiro',
      'RECOMMENDATION': 'Recomendação',
      'TRADE_SHOW': 'Feira/Evento',
      'WEB_FORM': 'Formulário Web',
      'CALLBACK': 'Callback',
      'RC_GENERATOR': 'Gerador RC',
      'STORE': 'Loja',
      'OTHER': 'Outro'
    };
    return origens[origemId] || origemId || 'Não especificado';
  }

  /**
   * Detecta se um texto parece ser nome de pessoa ou nome de empresa
   */
  private isNomePessoa(texto: string): boolean {
    if (!texto || typeof texto !== 'string') return false;

    const textoLimpo = texto.trim().toLowerCase();

    // Indicadores de que é uma empresa (não pessoa)
    const indicadoresEmpresa = [
      'ltda', 'ltd', 'me', 'epp', 'eireli', 'sa', 's.a', 's/a',
      'delivery', 'restaurante', 'lanchonete', 'pizzaria', 'bar', 'grill',
      'hamburgueria', 'sorveteria', 'açaiteria', 'café', 'padaria',
      'empresa', 'comercio', 'comércio', 'distribuidora', 'food',
      'foods', 'express', 'fast', 'prime', 'premium', 'burger',
      'house', 'place', 'center', 'centro', 'grupo', 'cia', 'corp'
    ];

    // Se contém indicadores de empresa, não é nome de pessoa
    if (indicadoresEmpresa.some(indicador => textoLimpo.includes(indicador))) {
      return false;
    }

    // Se tem números, provavelmente é empresa
    if (/\d/.test(textoLimpo)) {
      return false;
    }

    // Se tem apenas uma palavra e é muito curta (menos de 3 caracteres), provavelmente não é nome
    const palavras = textoLimpo.split(/\s+/).filter(p => p.length > 0);
    if (palavras.length === 1 && palavras[0].length < 3) {
      return false;
    }

    // Se tem 1-3 palavras e não contém indicadores de empresa, pode ser nome de pessoa
    if (palavras.length >= 1 && palavras.length <= 3) {
      return true;
    }

    // Muitas palavras provavelmente é nome de empresa
    return false;
  }

  /**
   * Converte dados do Bitrix24 para uma instância da classe Lead do sistema
   */
  converterParaLead(dadosBitrix: any, crmEmpresaId: number): any {
    try {
      console.log('BITRIX: Convertendo dados do Bitrix24 para Lead do sistema');

      // Importar a classe Lead dinamicamente para evitar dependência circular
      const { Lead } = require('../../domain/crm/Lead');
      const { OrigemLead, EtapaFunilLead, SegmentoLead } = require('../../domain/crm/LeadEnums');
      const { TipoTelefoneLead } = require('../../domain/crm/CrmTelefoneLead');
      const { TipoLinkLead } = require('../../domain/crm/LeadLink');

      const lead = new Lead();

      // ===== DADOS BÁSICOS =====
      lead.id = dadosBitrix.id ? parseInt(dadosBitrix.id, 10) : undefined;
      lead.idBitrix = dadosBitrix.id ? parseInt(dadosBitrix.id, 10) : undefined;
      lead.crmEmpresaId = crmEmpresaId;

      // Nome do responsável (priorizar NOME + SOBRENOME, validar TÍTULO)
      const nome = dadosBitrix.nome || dadosBitrix.NAME || '';
      const sobrenome = dadosBitrix.sobrenome || dadosBitrix.LAST_NAME || '';
      const titulo = dadosBitrix.titulo || dadosBitrix.TITLE || '';

      // Primeiro, tentar usar nome completo ou combinação de nome + sobrenome
      let nomeResponsavel = dadosBitrix.nomeCompleto ||
                           (nome && sobrenome ? `${nome} ${sobrenome}` : nome || sobrenome);

      // Se não tem nome/sobrenome, verificar se o título parece ser nome de pessoa
      if (!nomeResponsavel && titulo) {
        if (this.isNomePessoa(titulo)) {
          nomeResponsavel = titulo;
          console.log(`BITRIX: Título "${titulo}" identificado como nome de pessoa`);
        } else {
          console.log(`BITRIX: Título "${titulo}" identificado como nome de empresa, não será usado como responsável`);
        }
      }

      lead.nomeResponsavel = nomeResponsavel || 'Não informado';

      // Empresa (priorizar COMPANY_TITLE, mas usar TITLE se parece ser empresa)
      let nomeEmpresa = dadosBitrix.empresa || dadosBitrix.COMPANY_TITLE;

      // Se não tem nome de empresa explícito, verificar se o título é nome de empresa
      if (!nomeEmpresa && titulo && !this.isNomePessoa(titulo)) {
        nomeEmpresa = titulo;
        console.log(`BITRIX: Título "${titulo}" usado como nome da empresa`);
      }

      lead.empresa = nomeEmpresa || 'Empresa não informada';

      // Telefone principal (primeiro telefone disponível)
      const telefones = dadosBitrix.telefones || [];
      if (telefones.length > 0) {
        lead.telefone = telefones[0].numero.replace(/\D/g, '');
      } else if (dadosBitrix.PHONE && Array.isArray(dadosBitrix.PHONE) && dadosBitrix.PHONE.length > 0) {
        lead.telefone = dadosBitrix.PHONE[0].VALUE?.replace(/\D/g, '') || '';
      }

      // ===== ENDEREÇO =====
      const endereco = dadosBitrix.endereco || {};
      if (endereco.enderecoCompleto) {
        lead.endereco = endereco.enderecoCompleto;
      } else if (endereco.rua) {
        const partesEndereco = [];
        if (endereco.rua) partesEndereco.push(endereco.rua);
        if (endereco.cidade) partesEndereco.push(endereco.cidade);
        if (endereco.estado) partesEndereco.push(endereco.estado);
        lead.endereco = partesEndereco.join(', ');
      }

      lead.cidade = endereco.cidade || dadosBitrix.ADDRESS_CITY || '';

      // ===== ORIGEM E ETAPA =====
      lead.origem = this.mapearOrigemBitrixParaSistema(dadosBitrix.origem?.id || dadosBitrix.SOURCE_ID);
      lead.etapa = this.mapearStatusBitrixParaEtapa(dadosBitrix.status?.id || dadosBitrix.STATUS_ID);

      // ===== DATAS =====
      if (dadosBitrix.dataCriacao || dadosBitrix.DATE_CREATE) {
        lead.dataCriacao = new Date(dadosBitrix.dataCriacao || dadosBitrix.DATE_CREATE);
      }

      if (dadosBitrix.dataModificacao || dadosBitrix.DATE_MODIFY) {
        lead.dataUltimaInteracao = new Date(dadosBitrix.dataModificacao || dadosBitrix.DATE_MODIFY);
      }

      if (dadosBitrix.dataFechamento || dadosBitrix.DATE_CLOSED) {
        lead.dataFechamento = new Date(dadosBitrix.dataFechamento || dadosBitrix.DATE_CLOSED);
      }

      // ===== VALOR POTENCIAL =====
      if (dadosBitrix.oportunidade || dadosBitrix.OPPORTUNITY) {
        const valor = parseFloat(dadosBitrix.oportunidade || dadosBitrix.OPPORTUNITY);
        if (!isNaN(valor) && valor > 0) {
          lead.valorPotencial = valor;
        }
      }

      // ===== OBSERVAÇÕES =====
      lead.observacoes = dadosBitrix.observacoes || dadosBitrix.COMMENTS || '';
      lead.notas = `Lead importado do Bitrix24 (ID: ${dadosBitrix.id || 'N/A'})`;

      // ===== TELEFONES ESTRUTURADOS =====
      if (telefones.length > 0) {
        lead.telefones = [];
        telefones.forEach((telefoneData: any, index: number) => {
          const tipo = this.mapearTipoTelefoneBitrixParaSistema(telefoneData.tipo);
          const numeroLimpo = telefoneData.numero.replace(/\D/g, '');

          if (numeroLimpo.length >= 10) {
            const { CrmTelefoneLead } = require('../../domain/crm/CrmTelefoneLead');
            const telefoneObj = new CrmTelefoneLead(
              lead.id || 0,
              tipo,
              numeroLimpo,
              telefoneData.tipo,
              index
            );
            lead.telefones.push(telefoneObj);
          }
        });
      }

      // ===== LINKS/WEBSITES =====
      lead.links = [];
      let linkIndex = 0;

      // Processar websites básicos
      const websites = dadosBitrix.website || [];
      websites.forEach((websiteData: any) => {
        const { LeadLink, TipoLinkLead } = require('../../domain/crm/LeadLink');
        const linkObj = new LeadLink(
          lead.id || 0,
          TipoLinkLead.Site,
          websiteData.url,
          websiteData.tipo,
          linkIndex++
        );
        lead.links.push(linkObj);

        // Definir website principal
        if (linkIndex === 1) {
          lead.website = websiteData.url;
        }
      });

      // ===== PROCESSAR CAMPO CUSTOMIZADO UF_CRM_1749901110452 (LINKS) =====
      if (dadosBitrix.camposCustomizados && dadosBitrix.camposCustomizados['UF_CRM_1749901110452']) {
        const linksCustomizados = dadosBitrix.camposCustomizados['UF_CRM_1749901110452'];

        if (Array.isArray(linksCustomizados)) {
          console.log(`BITRIX: Processando ${linksCustomizados.length} links do campo customizado UF_CRM_1749901110452`);

          const { LeadLink, TipoLinkLead } = require('../../domain/crm/LeadLink');
          const linksUnicos = new Set(); // Para evitar duplicatas

          linksCustomizados.forEach((url: string) => {
            if (url && typeof url === 'string' && url.trim() !== '' && !linksUnicos.has(url)) {
              linksUnicos.add(url);

              // Detectar tipo do link baseado na URL
              const tipoLink = this.detectarTipoLinkPorUrl(url);
              const descricao = this.obterDescricaoLinkPorTipo(tipoLink, url);

              const linkObj = new LeadLink(
                lead.id || 0,
                tipoLink,
                url,
                descricao,
                linkIndex++
              );

              lead.links.push(linkObj);

              // Definir website principal se ainda não foi definido
              if (!lead.website && (tipoLink === TipoLinkLead.Site || url.includes('.com'))) {
                lead.website = url;
              }

              // Extrair Instagram handle se for link do Instagram
              if (tipoLink === TipoLinkLead.Instagram && url.includes('instagram.com/')) {
                const match = url.match(/instagram\.com\/([^\/\?]+)/);
                if (match && match[1]) {
                  lead.instagramHandle = match[1];
                  lead.linkInsta = url;
                }
              }
            }
          });

          console.log(`BITRIX: Adicionados ${linksUnicos.size} links únicos do campo customizado`);
        }
      }

      // ===== SEGMENTO (DETECTAR AUTOMATICAMENTE) =====
      lead.segmento = this.detectarSegmentoPorEmpresa(lead.empresa);

      // ===== SCORE INICIAL =====
      lead.score = this.calcularScoreInicial(lead);

      // ===== CAMPOS CUSTOMIZADOS =====
      if (dadosBitrix.camposCustomizados) {
        // Procurar por Instagram nos campos customizados
        Object.keys(dadosBitrix.camposCustomizados).forEach(campo => {
          const valor = dadosBitrix.camposCustomizados[campo];
          if (campo.toLowerCase().includes('instagram') && valor) {
            lead.instagramHandle = valor.replace('@', '').trim();
            lead.linkInsta = `https://instagram.com/${lead.instagramHandle}`;
          }
        });
      }

      // ===== CRIAR OBJETO CrmEmpresa =====
      const { CrmEmpresa } = require('../../domain/crm/CrmEmpresa');
      const crmEmpresa = new CrmEmpresa(lead.empresa);

      // Configurar dados da empresa
      crmEmpresa.id = crmEmpresaId;
      crmEmpresa.nome = lead.empresa;
      crmEmpresa.telefone = lead.telefone;
      crmEmpresa.endereco = lead.endereco;
      crmEmpresa.ativa = true;

      // Extrair CNPJ se disponível nos campos customizados ou observações
      if (dadosBitrix.observacoes && dadosBitrix.observacoes.includes('Razão Social:')) {
        // Tentar extrair CNPJ das observações se houver
        const cnpjMatch = dadosBitrix.observacoes.match(/(\d{2}\.?\d{3}\.?\d{3}\/?\d{4}-?\d{2})/);
        if (cnpjMatch) {
          crmEmpresa.cnpj = cnpjMatch[1].replace(/\D/g, '');
        }
      }

      // Criar sócio principal baseado no responsável do lead
      if (lead.nomeResponsavel && lead.nomeResponsavel !== 'Não informado') {
        const socioPrincipal = {
          nome: lead.nomeResponsavel,
          cargo: 'Responsável',
          principal: true,
          dataEntrada: lead.dataCriacao ? lead.dataCriacao.toISOString().split('T')[0] : undefined,
          scoreAnalise: lead.score,
          motivoSelecao: 'Responsável identificado no Bitrix24'
        };
        crmEmpresa.adicionarSocio(socioPrincipal);
      }

      // Associar empresa ao lead
      lead.crmEmpresa = crmEmpresa;

      console.log(`BITRIX: Lead convertido com sucesso - ${lead.nomeResponsavel} (${lead.empresa})`);
      console.log(`BITRIX: CrmEmpresa criada - ${crmEmpresa.nome} (ID: ${crmEmpresa.id})`);
      return lead;

    } catch (error) {
      console.error('BITRIX: Erro ao converter dados para Lead:', error);
      throw new Error(`Erro ao converter dados do Bitrix24: ${error.message}`);
    }
  }

  /**
   * Mapeia origem do Bitrix24 para OrigemLead do sistema
   */
  private mapearOrigemBitrixParaSistema(origemBitrix: string): any {
    const { OrigemLead } = require('../../domain/crm/LeadEnums');

    const mapeamento: { [key: string]: any } = {
      'WEB': OrigemLead.SiteLandingPage,
      'WEB_FORM': OrigemLead.SiteLandingPage,
      'CALL': OrigemLead.WhatsappDireto,
      'CALLBACK': OrigemLead.WhatsappDireto,
      'EMAIL': OrigemLead.Outros,
      'ADVERTISING': OrigemLead.Outros,
      'PARTNER': OrigemLead.Indicacao,
      'RECOMMENDATION': OrigemLead.Indicacao,
      'TRADE_SHOW': OrigemLead.EventoFeira,
      'STORE': OrigemLead.Outros,
      'OTHER': OrigemLead.Outros
    };

    return mapeamento[origemBitrix] || OrigemLead.Outros;
  }

  /**
   * Mapeia status do Bitrix24 para EtapaFunilLead do sistema
   */
  private mapearStatusBitrixParaEtapa(statusBitrix: string): any {
    const { EtapaFunilLead } = require('../../domain/crm/LeadEnums');

    const mapeamento: { [key: string]: any } = {
      'NEW': EtapaFunilLead.Prospecção,
      'IN_PROCESS': EtapaFunilLead.Qualificação,
      'PROCESSED': EtapaFunilLead.Objeção,
      'CONVERTED': EtapaFunilLead.Ganho,
      'JUNK': EtapaFunilLead.Perdido
    };

    return mapeamento[statusBitrix] || EtapaFunilLead.Prospecção;
  }

  /**
   * Mapeia tipo de telefone do Bitrix24 para TipoTelefoneLead do sistema
   */
  private mapearTipoTelefoneBitrixParaSistema(tipoBitrix: string): any {
    const { TipoTelefoneLead } = require('../../domain/crm/CrmTelefoneLead');

    const mapeamento: { [key: string]: any } = {
      'Celular': TipoTelefoneLead.Celular,
      'Comercial': TipoTelefoneLead.Comercial,
      'Residencial': TipoTelefoneLead.TelefoneFixo,
      'Outro': TipoTelefoneLead.Celular
    };

    return mapeamento[tipoBitrix] || TipoTelefoneLead.Celular;
  }

  /**
   * Detecta segmento baseado no nome da empresa
   */
  private detectarSegmentoPorEmpresa(nomeEmpresa: string): any {
    if (!nomeEmpresa) return null;

    const { SegmentoLead } = require('../../domain/crm/LeadEnums');
    const nomeEmpresaLower = nomeEmpresa.toLowerCase();

    // Mapeamento de palavras-chave para segmentos
    const segmentos = [
      { palavras: ['pizzaria', 'pizza'], segmento: SegmentoLead.Pizzaria },
      { palavras: ['hamburgueria', 'burger', 'hambúrguer'], segmento: SegmentoLead.Hamburgueria },
      { palavras: ['lanchonete', 'lanche', 'sanduíche'], segmento: SegmentoLead.Lanchonete },
      { palavras: ['confeitaria', 'doceria', 'doce', 'bolo'], segmento: SegmentoLead.Confeitaria },
      { palavras: ['bar', 'boteco', 'cervejaria'], segmento: SegmentoLead.Bar },
      { palavras: ['food truck', 'foodtruck'], segmento: SegmentoLead.FoodTruck },
      { palavras: ['restaurante', 'gastronomi'], segmento: SegmentoLead.Restaurante }
    ];

    // Procurar por palavras-chave no nome da empresa
    for (const item of segmentos) {
      for (const palavra of item.palavras) {
        if (nomeEmpresaLower.includes(palavra)) {
          return item.segmento;
        }
      }
    }

    // Padrão: Restaurante
    return SegmentoLead.Restaurante;
  }

  /**
   * Calcula score inicial baseado nos dados disponíveis
   */
  private calcularScoreInicial(lead: any): number {
    let score = 0;

    // Dados básicos completos (+20)
    if (lead.nomeResponsavel && lead.empresa && lead.telefone) {
      score += 20;
    }

    // Tem endereço (+10)
    if (lead.endereco) {
      score += 10;
    }

    // Tem website (+10)
    if (lead.website) {
      score += 10;
    }

    // Tem múltiplos telefones (+5)
    if (lead.telefones && lead.telefones.length > 1) {
      score += 5;
    }

    // Tem valor potencial (+15)
    if (lead.valorPotencial && lead.valorPotencial > 0) {
      score += 15;
    }

    // Lead recente (criado nos últimos 30 dias) (+10)
    if (lead.dataCriacao) {
      const diasAtras = (new Date().getTime() - lead.dataCriacao.getTime()) / (1000 * 60 * 60 * 24);
      if (diasAtras <= 30) {
        score += 10;
      }
    }

    // Empresa com nome específico (não genérico) (+5)
    if (lead.empresa && !lead.empresa.toLowerCase().includes('empresa') &&
        !lead.empresa.toLowerCase().includes('não informad')) {
      score += 5;
    }

    // Máximo de 75 para leads importados (deixar espaço para crescimento)
    return Math.min(score, 75);
  }

  /**
   * Detecta o tipo de link baseado na URL
   */
  private detectarTipoLinkPorUrl(url: string): any {
    if (!url) return null;

    const { TipoLinkLead } = require('../../domain/crm/LeadLink');
    const urlLower = url.toLowerCase();

    // WhatsApp
    if (urlLower.includes('wa.me') || urlLower.includes('whatsapp.com') || urlLower.includes('api.whatsapp.com')) {
      return TipoLinkLead.Whatsapp;
    }

    // Instagram
    if (urlLower.includes('instagram.com')) {
      return TipoLinkLead.Instagram;
    }

    // iFood específico
    if (urlLower.includes('ifood')) {
      return TipoLinkLead.Ifood;
    }

    // Delivery/Cardápio
    if (urlLower.includes('goomer') || urlLower.includes('delivery') ||
        urlLower.includes('cardapio') || urlLower.includes('menu') ||
        urlLower.includes('pedido')) {
      return TipoLinkLead.SiteCardapio;
    }

    // Localização/Maps
    if (urlLower.includes('maps.google') || urlLower.includes('goo.gl/maps') ||
        urlLower.includes('maps.app.goo.gl')) {
      return TipoLinkLead.Localizacao;
    }

    // Site principal (padrão)
    return TipoLinkLead.Site;
  }

  /**
   * Gera descrição do link baseada no tipo e URL
   */
  private obterDescricaoLinkPorTipo(tipoLink: any, url: string): string {
    const { TipoLinkLead } = require('../../domain/crm/LeadLink');

    switch (tipoLink) {
      case TipoLinkLead.Whatsapp:
        return 'WhatsApp';
      case TipoLinkLead.Instagram:
        return 'Instagram';
      case TipoLinkLead.Ifood:
        return 'iFood';
      case TipoLinkLead.SiteCardapio:
        if (url.includes('goomer')) return 'Cardápio Goomer';
        return 'Site do Cardápio';
      case TipoLinkLead.Localizacao:
        return 'Localização no Maps';
      case TipoLinkLead.Site:
      default:
        return 'Site';
    }
  }

  /**
   * Testa a conectividade com o Bitrix24
   * @returns Status da conexão
   */
  async testarConectividade(): Promise<Resposta<any>> {
    try {
      console.log('BITRIX: Testando conectividade...');

      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/app.info.json`;

      console.log('BITRIX: URL de teste:', url);

      const response = await axios.post(url, {});

      if (response.data.error) {
        console.error('BITRIX: Erro na conectividade:', response.data);
        return Resposta.erro(`Erro do Bitrix: ${response.data.error_description}`) as any;
      }

      console.log('BITRIX: Conectividade OK');
      return Resposta.sucesso({
        status: 'conectado',
        info: response.data.result,
        timestamp: new Date().toISOString()
      }) as Resposta<any>;

    } catch (error) {
      console.error('BITRIX: Erro ao testar conectividade:', error);

      if (error.response?.status === 401) {
        return Resposta.erro('Erro de autenticação - Webhook inválido ou expirado') as any;
      }

      if (error.response) {
        return Resposta.erro(`Erro HTTP ${error.response.status}: ${error.response.data?.error_description || error.message}`) as any;
      }

      return Resposta.erro(`Erro de conectividade: ${error.message}`) as any;
    }
  }

  /**
   * Busca responsáveis únicos dos leads (alternativa quando não há permissão user)
   * @returns Lista de responsáveis encontrados nos leads
   */
  async buscarResponsaveisDeLeads(): Promise<Resposta<any[]>> {
    try {
      console.log('BITRIX: Buscando responsáveis através dos leads...');

      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.lead.list.json`;

      const payload = {
        select: ['ASSIGNED_BY_ID', 'ID', 'TITLE', 'DATE_CREATE'],
        order: { 'DATE_CREATE': 'DESC' },
        filter: {},
        start: 0 // Buscar desde o início
      };

      console.log('BITRIX: URL:', url);
      console.log('BITRIX: Payload:', JSON.stringify(payload, null, 2));

      const response = await axios.post(url, payload);

      if (response.data.error) {
        console.error('BITRIX: Erro ao buscar leads:', response.data);
        return Resposta.erro(`Erro do Bitrix: ${response.data.error_description}`) as any;
      }

      const leads = response.data.result || [];
      const total = response.data.total || leads.length;
      const next = response.data.next || null;

      console.log('BITRIX: Leads encontrados nesta página:', leads.length);
      console.log('BITRIX: Total de leads no sistema:', total);
      console.log('BITRIX: Há mais páginas?', !!next);

      // Estatísticas detalhadas
      let leadsComResponsavel = 0;
      let leadsSemResponsavel = 0;
      const responsaveisUnicos = new Map();
      const estatisticasResponsaveis = new Map();

      leads.forEach((lead: any) => {
        if (lead.ASSIGNED_BY_ID && lead.ASSIGNED_BY_ID !== '0') {
          leadsComResponsavel++;

          if (!responsaveisUnicos.has(lead.ASSIGNED_BY_ID)) {
            responsaveisUnicos.set(lead.ASSIGNED_BY_ID, {
              id: lead.ASSIGNED_BY_ID,
              nome: `Usuário ${lead.ASSIGNED_BY_ID}`,
              nomeCompleto: `Usuário ${lead.ASSIGNED_BY_ID}`,
              tipo: 'responsavel_lead',
              quantidadeLeads: 0
            });
          }

          // Contar leads por responsável
          const count = estatisticasResponsaveis.get(lead.ASSIGNED_BY_ID) || 0;
          estatisticasResponsaveis.set(lead.ASSIGNED_BY_ID, count + 1);
        } else {
          leadsSemResponsavel++;
        }
      });

      // Atualizar quantidade de leads para cada responsável
      responsaveisUnicos.forEach((responsavel, id) => {
        responsavel.quantidadeLeads = estatisticasResponsaveis.get(id) || 0;
      });

      const responsaveis = Array.from(responsaveisUnicos.values());

      console.log('BITRIX: === ESTATÍSTICAS ===');
      console.log('BITRIX: Leads com responsável:', leadsComResponsavel);
      console.log('BITRIX: Leads sem responsável:', leadsSemResponsavel);
      console.log('BITRIX: Responsáveis únicos encontrados:', responsaveis.length);

      responsaveis.forEach(resp => {
        console.log(`BITRIX: - Usuário ${resp.id}: ${resp.quantidadeLeads} leads`);
      });

      // Adicionar informações extras no retorno
      const resultado = {
        responsaveis,
        estatisticas: {
          totalLeadsNestaPagina: leads.length,
          totalLeadsNoSistema: total,
          leadsComResponsavel,
          leadsSemResponsavel,
          responsaveisUnicos: responsaveis.length,
          temMaisPaginas: !!next
        }
      };

      return Resposta.sucesso(resultado) as Resposta<any>;

    } catch (error) {
      console.error('BITRIX: Erro ao buscar responsáveis:', error);

      if (error.response?.status === 401) {
        return Resposta.erro('Erro de autenticação com Bitrix24. Verifique as credenciais do webhook.') as any;
      }

      if (error.response) {
        return Resposta.erro(`Erro HTTP ${error.response.status}: ${error.response.data?.error_description || error.message}`) as any;
      }

      return Resposta.erro(`Erro ao buscar responsáveis: ${error.message}`) as any;
    }
  }

  /**
   * Busca TODOS os responsáveis de leads (paginando através de todas as páginas)
   * @returns Lista completa de responsáveis encontrados nos leads
   */
  async buscarTodosResponsaveisDeLeads(): Promise<Resposta<any[]>> {
    try {
      console.log('BITRIX: Buscando TODOS os responsáveis através de todos os leads...');

      const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/crm.lead.list.json`;

      const responsaveisUnicos = new Map();
      const estatisticasResponsaveis = new Map();
      let totalLeads = 0;
      let leadsComResponsavel = 0;
      let leadsSemResponsavel = 0;
      let start = 0;
      let hasMore = true;
      let pagina = 1;

      while (hasMore) {
        console.log(`BITRIX: Buscando página ${pagina} (start: ${start})`);

        const payload = {
          select: ['ASSIGNED_BY_ID', 'ID'],
          order: { 'DATE_CREATE': 'DESC' },
          filter: {},
          start: start
        };

        const response = await axios.post(url, payload);

        if (response.data.error) {
          console.error('BITRIX: Erro ao buscar leads:', response.data);
          return Resposta.erro(`Erro do Bitrix: ${response.data.error_description}`) as any;
        }

        const leads = response.data.result || [];
        const next = response.data.next || null;

        console.log(`BITRIX: Página ${pagina}: ${leads.length} leads`);

        totalLeads += leads.length;

        // Processar leads desta página
        leads.forEach((lead: any) => {
          if (lead.ASSIGNED_BY_ID && lead.ASSIGNED_BY_ID !== '0') {
            leadsComResponsavel++;

            if (!responsaveisUnicos.has(lead.ASSIGNED_BY_ID)) {
              responsaveisUnicos.set(lead.ASSIGNED_BY_ID, {
                id: lead.ASSIGNED_BY_ID,
                nome: `Usuário ${lead.ASSIGNED_BY_ID}`,
                nomeCompleto: `Usuário ${lead.ASSIGNED_BY_ID}`,
                tipo: 'responsavel_lead',
                quantidadeLeads: 0
              });
            }

            // Contar leads por responsável
            const count = estatisticasResponsaveis.get(lead.ASSIGNED_BY_ID) || 0;
            estatisticasResponsaveis.set(lead.ASSIGNED_BY_ID, count + 1);
          } else {
            leadsSemResponsavel++;
          }
        });

        // Verificar se há mais páginas
        hasMore = !!next && leads.length > 0;
        start = next || (start + 50);
        pagina++;

        // Pequena pausa para evitar rate limiting
        if (hasMore) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      // Atualizar quantidade de leads para cada responsável
      responsaveisUnicos.forEach((responsavel, id) => {
        responsavel.quantidadeLeads = estatisticasResponsaveis.get(id) || 0;
      });

      const responsaveis = Array.from(responsaveisUnicos.values());

      console.log('BITRIX: === ESTATÍSTICAS COMPLETAS ===');
      console.log('BITRIX: Total de páginas processadas:', pagina - 1);
      console.log('BITRIX: Total de leads processados:', totalLeads);
      console.log('BITRIX: Leads com responsável:', leadsComResponsavel);
      console.log('BITRIX: Leads sem responsável:', leadsSemResponsavel);
      console.log('BITRIX: Responsáveis únicos encontrados:', responsaveis.length);

      responsaveis.forEach(resp => {
        console.log(`BITRIX: - Usuário ${resp.id}: ${resp.quantidadeLeads} leads`);
      });

      // Adicionar informações extras no retorno
      const resultado = {
        responsaveis,
        estatisticas: {
          totalLeadsProcessados: totalLeads,
          paginasProcessadas: pagina - 1,
          leadsComResponsavel,
          leadsSemResponsavel,
          responsaveisUnicos: responsaveis.length
        }
      };

      return Resposta.sucesso(resultado) as Resposta<any>;

    } catch (error) {
      console.error('BITRIX: Erro ao buscar todos os responsáveis:', error);

      if (error.response?.status === 401) {
        return Resposta.erro('Erro de autenticação com Bitrix24. Verifique as credenciais do webhook.') as any;
      }

      if (error.response) {
        return Resposta.erro(`Erro HTTP ${error.response.status}: ${error.response.data?.error_description || error.message}`) as any;
      }

      return Resposta.erro(`Erro ao buscar todos os responsáveis: ${error.message}`) as any;
    }
  }

  /**
   * Busca usuários do Bitrix24
   * @param filtro Filtros opcionais (active: true/false, admin: true/false)
   * @returns Lista de usuários do Bitrix
   */
  async buscarUsuarios(filtro?: { active?: boolean; admin?: boolean }): Promise<Resposta<any[]>> {
    const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/user.get.json`;

    try {
      console.log('BITRIX: Buscando usuários...');

      // Construir payload para POST
      const payload: any = {};

      if (filtro) {
        const filter: any = {};
        if (filtro.active !== undefined) {
          filter.ACTIVE = filtro.active;
        }
        if (filtro.admin !== undefined) {
          filter.ADMIN = filtro.admin;
        }
        if (Object.keys(filter).length > 0) {
          payload.FILTER = filter;
        }
      }

      console.log('BITRIX: URL:', url);
      console.log('BITRIX: Payload:', JSON.stringify(payload, null, 2));

      const response = await axios.post(url, payload);

      if (response.data.error) {
        console.error('BITRIX: Erro ao buscar usuários:', response.data);
        return Resposta.erro(`Erro do Bitrix: ${response.data.error_description}`) as any;
      }

      const usuarios = response.data.result || [];
      console.log('BITRIX: Usuários encontrados:', usuarios.length);

      // Formatar dados dos usuários
      const usuariosFormatados = usuarios.map((user: any) => ({
        id: user.ID,
        nome: user.NAME,
        sobrenome: user.LAST_NAME,
        nomeCompleto: `${user.NAME || ''} ${user.LAST_NAME || ''}`.trim(),
        email: user.EMAIL,
        ativo: user.ACTIVE === true || user.ACTIVE === 'Y',
        admin: user.ADMIN === true || user.ADMIN === 'Y',
        departamentos: user.UF_DEPARTMENT || [],
        cargo: user.WORK_POSITION,
        telefone: user.WORK_PHONE,
        celular: user.PERSONAL_MOBILE,
        foto: user.PERSONAL_PHOTO,
        dataRegistro: user.DATE_REGISTER,
        ultimoLogin: user.LAST_LOGIN
      }));

      return Resposta.sucesso(usuariosFormatados) as Resposta<any[]>;

    } catch (error) {
      console.error('BITRIX: Erro ao buscar usuários:', error);

      // Tratamento específico para erro 401
      if (error.response?.status === 401) {
        console.error('BITRIX: Erro de autenticação (401) - Webhook pode estar expirado ou inválido');
        console.error('BITRIX: URL usada:', url);
        console.error('BITRIX: Config:', {
          baseUrl: this.config.baseUrl,
          userId: this.config.userId,
          webhook: this.config.webhook ? '***' : 'undefined'
        });
        return Resposta.erro('Erro de autenticação com Bitrix24. Verifique as credenciais do webhook.') as any;
      }

      // Tratamento para outros erros HTTP
      if (error.response) {
        console.error('BITRIX: Status HTTP:', error.response.status);
        console.error('BITRIX: Dados da resposta:', error.response.data);
        return Resposta.erro(`Erro HTTP ${error.response.status}: ${error.response.data?.error_description || error.message}`) as any;
      }

      return Resposta.erro(`Erro ao buscar usuários: ${error.message}`) as any;
    }
  }

  /**
   * Busca um usuário específico do Bitrix24 pelo ID
   * @param userId ID do usuário
   * @returns Dados do usuário
   */
  async buscarUsuarioPorId(userId: number): Promise<Resposta<any>> {
    const url = `${this.config.baseUrl}/rest/${this.config.userId}/${this.config.webhook}/user.get.json`;

    try {
      console.log('BITRIX: Buscando usuário por ID:', userId);

      const payload = {
        ID: userId
      };

      console.log('BITRIX: URL:', url);
      console.log('BITRIX: Payload:', JSON.stringify(payload, null, 2));

      const response = await axios.post(url, payload);

      if (response.data.error) {
        console.error('BITRIX: Erro ao buscar usuário:', response.data);
        return Resposta.erro(`Erro do Bitrix: ${response.data.error_description}`) as any;
      }

      const usuarios = response.data.result || [];

      if (usuarios.length === 0) {
        return Resposta.erro(`Usuário com ID ${userId} não encontrado`) as any;
      }

      const user = usuarios[0];

      // Formatar dados do usuário
      const usuarioFormatado = {
        id: user.ID,
        nome: user.NAME,
        sobrenome: user.LAST_NAME,
        nomeCompleto: `${user.NAME || ''} ${user.LAST_NAME || ''}`.trim(),
        email: user.EMAIL,
        ativo: user.ACTIVE === true || user.ACTIVE === 'Y',
        admin: user.ADMIN === true || user.ADMIN === 'Y',
        departamentos: user.UF_DEPARTMENT || [],
        cargo: user.WORK_POSITION,
        telefone: user.WORK_PHONE,
        celular: user.PERSONAL_MOBILE,
        foto: user.PERSONAL_PHOTO,
        dataRegistro: user.DATE_REGISTER,
        ultimoLogin: user.LAST_LOGIN
      };

      return Resposta.sucesso(usuarioFormatado) as Resposta<any>;

    } catch (error) {
      console.error('BITRIX: Erro ao buscar usuário:', error);
      return Resposta.erro(`Erro ao buscar usuário: ${error.message}`) as any;
    }
  }
}

/**
 * Factory para criar instância do BitrixService com configuração padrão
 */
export class BitrixServiceFactory {

  /**
   * Mapeamento de emails de usuários do PromoKit para configurações do Bitrix24
   */
  private static readonly USER_MAPPING: { [email: string]: { userId: string, webhook: string } } = {
    '<EMAIL>': { userId: '1', webhook: 'zlr1ejoyx1m4urgr' },
    '<EMAIL>': { userId: '51', webhook: 'zlr1ejoyx1m4urgr' }
    // Adicionar novos usuários conforme necessário
  };

  /**
   * Cria instância do BitrixService para um usuário específico baseado no email
   * @param emailUsuario Email do usuário logado no PromoKit
   * @returns Instância do BitrixService configurada para o usuário
   */
  static criarInstanciaParaUsuario(emailUsuario: string): BitrixService {
    // Buscar configuração do Bitrix baseada no email
    const userConfig = this.USER_MAPPING[emailUsuario?.toLowerCase()];

    // Fallback para configuração da Thalita se não encontrar
    const config = userConfig || this.USER_MAPPING['<EMAIL>'];

    console.log(`BITRIX_FACTORY: Criando instância para usuário: ${emailUsuario} → Bitrix userId: ${config.userId}, webhook: ${config.webhook}`);

    if (!userConfig) {
      console.warn(`BITRIX_FACTORY: Email ${emailUsuario} não encontrado no mapeamento. Usando configuração padrão da Thalita.`);
    }

    const bitrixConfig: BitrixConfig = {
      baseUrl: 'https://b24-chlbsw.bitrix24.com.br',
      userId: config.userId,
      webhook: config.webhook
    };

    return new BitrixService(bitrixConfig);
  }

  /**
   * @deprecated Use criarInstanciaParaUsuario() em vez disso
   * Método mantido para compatibilidade, mas deve ser substituído
   */
  static criarInstancia(): BitrixService {
    console.warn('BITRIX_FACTORY: Usando criarInstancia() deprecado. Use criarInstanciaParaUsuario() em vez disso.');

    const config: BitrixConfig = {
      baseUrl: 'https://b24-chlbsw.bitrix24.com.br',
      userId: '1',
      webhook: 'zlr1ejoyx1m4urgr'
    };

    return new BitrixService(config);
  }

  static criarInstanciaPersonalizada(baseUrl: string, userId: string, webhook: string): BitrixService {
    const config: BitrixConfig = {
      baseUrl,
      userId,
      webhook
    };

    return new BitrixService(config);
  }

  /**
   * Adiciona ou atualiza mapeamento de usuário
   * @param email Email do usuário
   * @param userId ID do usuário no Bitrix24
   * @param webhook Webhook do usuário no Bitrix24
   */
  static adicionarMapeamento(email: string, userId: string, webhook: string): void {
    this.USER_MAPPING[email.toLowerCase()] = { userId, webhook };
    console.log(`BITRIX_FACTORY: Mapeamento adicionado: ${email} → userId: ${userId}, webhook: ${webhook}`);
  }

  /**
   * Lista todos os mapeamentos de usuários
   * @returns Objeto com todos os mapeamentos email → configuração
   */
  static listarMapeamentos(): { [email: string]: { userId: string, webhook: string } } {
    return { ...this.USER_MAPPING };
  }
}
